import React, { useState, useEffect } from "react";
import {
  FocusModal,
  Heading,
  Button,
  Label,
} from "@camped-ai/ui";
import { Download } from "lucide-react";

interface ExportModalProps {
  open: boolean;
  onClose: () => void;
  hotelId?: string;
}

const ExportModal: React.FC<ExportModalProps> = ({ open, onClose, hotelId }) => {
  // State for selected fields
  const [selectedFields, setSelectedFields] = useState<Record<string, boolean>>({
    id: true,
    title: true,
    handle: true,
    description: true,
    is_active: true,
    hotel_id: true,
    bed_type: true,
    max_occupancy: true,
    min_occupancy: true,
    max_adults: true,
    max_children: true,
    is_featured: true,
    amenities: true,
    created_at: true,
    updated_at: true,
  });

  // State for filters
  const [filters, setFilters] = useState({
    is_active: "all", // "all", "true", "false"
    hotel_id: hotelId || "all", // Default to current hotel or "all"
  });

  // State for available hotels
  const [hotels, setHotels] = useState<Array<{id: string, name: string}>>([]);
  const [isLoadingHotels, setIsLoadingHotels] = useState(false);

  // State for file format
  const [fileFormat, setFileFormat] = useState<"csv" | "xlsx">("xlsx");

  // Fetch hotels when modal opens
  useEffect(() => {
    if (open) {
      fetchHotels();
    }
  }, [open]);

  // Update hotel filter when hotelId prop changes
  useEffect(() => {
    if (hotelId) {
      setFilters(prev => ({
        ...prev,
        hotel_id: hotelId
      }));
    }
  }, [hotelId]);

  // Fetch hotels for the dropdown
  const fetchHotels = async () => {
    setIsLoadingHotels(true);
    try {
      const response = await fetch('/admin/hotel-management/hotels', {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to fetch hotels');
      }

      const { hotels: hotelsList } = await response.json();

      // Format hotels for the dropdown
      const formattedHotels = hotelsList.map((hotel: any) => ({
        id: hotel.id,
        name: hotel.name
      }));

      setHotels(formattedHotels);
    } catch (error) {
      console.error('Error fetching hotels:', error);
    } finally {
      setIsLoadingHotels(false);
    }
  };

  // Handle field selection
  const handleFieldChange = (field: string, checked: boolean) => {
    setSelectedFields((prev) => ({
      ...prev,
      [field]: checked,
    }));
  };

  // Handle select all fields
  const handleSelectAll = () => {
    const allFields = { ...selectedFields };
    const allSelected = Object.values(allFields).every((value) => value);

    Object.keys(allFields).forEach((key) => {
      allFields[key] = !allSelected;
    });

    setSelectedFields(allFields);
  };

  // Handle filter changes
  const handleFilterChange = (
    filterName: string,
    value: string | boolean
  ) => {
    setFilters((prev) => ({
      ...prev,
      [filterName]: value,
    }));
  };

  // Handle export
  const handleExport = async () => {
    try {
      console.log('Starting export with filters:', filters);
      console.log('Export modal hotelId prop:', hotelId);

      // Build query parameters
      const queryParams = new URLSearchParams();

      // Add filters
      if (filters.is_active !== "all") {
        queryParams.append("is_active", filters.is_active);
      }

      if (filters.hotel_id !== "all") {
        queryParams.append("hotel_id", filters.hotel_id);
        console.log('Adding hotel_id to export:', filters.hotel_id);
      }

      // Add format
      queryParams.append("format", fileFormat);

      // Use the simplified export endpoint
      const exportUrl = `/admin/room-configs/export-simple?${queryParams.toString()}`;
      console.log('Export URL:', exportUrl);

      // Open in a new tab for download
      window.open(exportUrl, '_blank');

      console.log('Export request successful');

      // Show success message
      try {
        const { toast } = await import("@camped-ai/ui");
        toast.success("Export Started", {
          description: "Your export is being processed and will download shortly.",
        });
      } catch (e) {
        // Ignore toast error
      }

      // Close the modal
      onClose();
    } catch (error) {
      console.error("Error exporting room configurations:", error);

      // Show error toast
      try {
        // Try to import toast from @camped-ai/ui
        const { toast } = await import("@camped-ai/ui");
        toast.error("Export Error", {
          description: (error as Error).message || "Failed to export room configurations",
        });
      } catch (e) {
        // Fallback to alert if toast import fails
        alert(`Export Error: ${(error as Error).message || "Failed to export room configurations"}`);
      }
    }
  };

  return (
    <FocusModal open={open} onOpenChange={onClose}>
      <FocusModal.Content className="flex flex-col h-full max-h-[98vh] bg-gray-50 dark:bg-gray-900">
        <FocusModal.Header className="flex-shrink-0 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
          <div className="flex justify-between items-center w-full py-4 px-6">
            <div className="flex items-center gap-3">
              <Heading level="h2" className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                Export Room Configurations
              </Heading>
            </div>

          {/* Progress Indicator */}
          <div className="px-6 py-2 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                {/* Step 1 */}
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-blue-600 dark:bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-semibold">
                    1
                  </div>
                  <span className="ml-2 text-sm font-medium text-blue-600 dark:text-blue-400">Configure</span>
                </div>
                <div className="w-8 h-0.5 bg-gray-300 dark:bg-gray-600"></div>

                {/* Step 2 */}
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 text-gray-600 dark:text-gray-400 rounded-full flex items-center justify-center text-sm font-semibold">
                    2
                  </div>
                  <span className="ml-2 text-sm font-medium text-gray-500 dark:text-gray-400">Export</span>
                </div>
              </div>
            </div>
          </div>
          </div>

        </FocusModal.Header>
        <FocusModal.Body className="flex flex-col flex-grow overflow-hidden">
          <div className="flex-grow overflow-hidden p-6">
            <div className="flex flex-col gap-6 h-full">
              {/* Fields Selection Section - Full Width Top Row */}
              <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                <div className="flex-shrink-0 p-4 border-b border-gray-200 dark:border-gray-700">
                  <div className="flex justify-between items-center">
                    <Heading level="h3" className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                      Select Fields to Export
                    </Heading>
                    <Button
                      variant="secondary"
                      size="small"
                      onClick={handleSelectAll}
                      className="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 border-gray-300 dark:border-gray-600"
                    >
                      {Object.values(selectedFields).every((v) => v)
                        ? "Deselect All"
                        : "Select All"}
                    </Button>
                  </div>
                </div>

                <div className="p-4">
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-3">
                    {Object.keys(selectedFields).map((field) => (
                      <div key={field} className="flex items-center gap-2 p-2 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors border border-gray-100 dark:border-gray-600">
                        <input
                          type="checkbox"
                          id={`field-${field}`}
                          checked={selectedFields[field]}
                          onChange={(e) => handleFieldChange(field, e.target.checked)}
                          className="h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400 rounded border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700"
                        />
                        <Label htmlFor={`field-${field}`} className="cursor-pointer text-gray-700 dark:text-gray-300 text-sm font-medium flex-grow">
                          {field
                            .replace(/_/g, " ")
                            .replace(/\\b\\w/g, (l) => l.toUpperCase())}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Bottom Row - Filters, Format, and Summary */}
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 flex-grow">

                {/* Filters Section */}
                <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                  <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                    <Heading level="h3" className="text-base font-semibold text-gray-900 dark:text-gray-100">
                      Filters
                    </Heading>
                  </div>

                  <div className="p-4 space-y-4">
                    <div>
                      <Label htmlFor="is_active" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Active Status</Label>
                      <select
                        id="is_active"
                        value={filters.is_active}
                        onChange={(e) => handleFilterChange("is_active", e.target.value)}
                        className="w-full px-3 py-2 text-sm border border-gray-200 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                      >
                        <option value="all">All Configurations</option>
                        <option value="true">Active Only</option>
                        <option value="false">Inactive Only</option>
                      </select>
                    </div>

                    <div>
                      <Label htmlFor="hotel_id" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Hotel</Label>
                      {isLoadingHotels ? (
                        <div className="flex items-center justify-center py-3 bg-gray-50 dark:bg-gray-700 rounded-md">
                          <div className="animate-spin h-4 w-4 border-2 border-blue-500 dark:border-blue-400 border-t-transparent rounded-full mr-2"></div>
                          <span className="text-sm text-gray-500 dark:text-gray-400">Loading hotels...</span>
                        </div>
                      ) : (
                        <select
                          id="hotel_id"
                          value={filters.hotel_id}
                          onChange={(e) => handleFilterChange("hotel_id", e.target.value)}
                          className="w-full px-3 py-2 text-sm border border-gray-200 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                        >
                          <option value="all">All Hotels</option>
                          {hotels.map((hotel) => (
                            <option key={hotel.id} value={hotel.id}>
                              {hotel.name}
                            </option>
                          ))}
                        </select>
                      )}
                    </div>
                  </div>
                </div>

                {/* Format Section */}
                <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                  <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                    <Heading level="h3" className="text-base font-semibold text-gray-900 dark:text-gray-100">
                      Export Format
                    </Heading>
                  </div>

                  <div className="p-4 space-y-3">
                    <div className="flex items-center gap-3 p-3 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors border border-gray-100 dark:border-gray-600">
                      <input
                        type="radio"
                        id="format-xlsx"
                        name="fileFormat"
                        value="xlsx"
                        checked={fileFormat === "xlsx"}
                        onChange={() => setFileFormat("xlsx")}
                        className="h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400 border-gray-300 dark:border-gray-600"
                      />
                      <div className="flex-grow">
                        <Label htmlFor="format-xlsx" className="cursor-pointer text-gray-900 dark:text-gray-100 font-medium">
                          Excel (.xlsx)
                        </Label>
                        <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">Recommended for data analysis</p>
                      </div>
                    </div>

                    <div className="flex items-center gap-3 p-3 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors border border-gray-100 dark:border-gray-600">
                      <input
                        type="radio"
                        id="format-csv"
                        name="fileFormat"
                        value="csv"
                        checked={fileFormat === "csv"}
                        onChange={() => setFileFormat("csv")}
                        className="h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400 border-gray-300 dark:border-gray-600"
                      />
                      <div className="flex-grow">
                        <Label htmlFor="format-csv" className="cursor-pointer text-gray-900 dark:text-gray-100 font-medium">
                          CSV (.csv)
                        </Label>
                        <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">Compatible with most applications</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Export Summary Section */}
                <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                  <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                    <Heading level="h3" className="text-base font-semibold text-gray-900 dark:text-gray-100">
                      Export Summary
                    </Heading>
                  </div>

                  <div className="p-4 space-y-4">
                    {/* Selected Fields Summary */}
                    <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3 border border-blue-200 dark:border-blue-800">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="text-sm font-semibold text-blue-900 dark:text-blue-100">Selected Fields</h4>
                        <span className="bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-200 text-xs font-medium px-2 py-1 rounded-full">
                          {Object.values(selectedFields).filter(Boolean).length} of {Object.keys(selectedFields).length}
                        </span>
                      </div>
                      <div className="text-xs text-blue-700 dark:text-blue-300 max-h-16 overflow-y-auto">
                        {Object.entries(selectedFields)
                          .filter(([_, selected]) => selected)
                          .slice(0, 4)
                          .map(([field]) => (
                            <div key={field} className="truncate">
                              • {field.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase())}
                            </div>
                          ))}
                        {Object.values(selectedFields).filter(Boolean).length > 4 && (
                          <div className="text-blue-600 dark:text-blue-400 font-medium">
                            +{Object.values(selectedFields).filter(Boolean).length - 4} more...
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Filter & Export Details Combined */}
                    <div className="grid grid-cols-2 gap-3">
                      <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-3 border border-green-200 dark:border-green-800">
                        <h4 className="text-sm font-semibold text-green-900 dark:text-green-100 mb-2">Filters</h4>
                        <div className="space-y-1 text-xs text-green-700 dark:text-green-300">
                          <div>
                            <span className="font-medium">Status: </span>
                            {filters.is_active === "all" ? "All" : filters.is_active === "true" ? "Active" : "Inactive"}
                          </div>
                          <div>
                            <span className="font-medium">Hotel: </span>
                            <span className="truncate">
                              {filters.hotel_id === "all" ? "All Hotels" : hotels.find(h => h.id === filters.hotel_id)?.name || "Selected"}
                            </span>
                          </div>
                        </div>
                      </div>

                      <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-3 border border-purple-200 dark:border-purple-800">
                        <h4 className="text-sm font-semibold text-purple-900 dark:text-purple-100 mb-2">Export Details</h4>
                        <div className="space-y-1 text-xs text-purple-700 dark:text-purple-300">
                          <div>
                            <span className="font-medium">Format: </span>
                            <span className="uppercase">{fileFormat}</span>
                          </div>
                          <div>
                            <span className="font-medium">Date: </span>
                            {new Date().toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Instructions */}
                    <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-3 border border-gray-200 dark:border-gray-700">
                      <div className="text-sm text-gray-600 dark:text-gray-400 text-center">
                        Click "Export Data" to download the file to your downloads folder
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="flex-shrink-0 py-6 px-8 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-500 dark:text-gray-400">
                Ready to export {Object.values(selectedFields).filter(Boolean).length} fields
              </div>
              <div className="flex gap-4">
                <Button
                  variant="secondary"
                  onClick={onClose}
                  className="px-6 py-3 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 border-gray-300 dark:border-gray-600 font-medium"
                >
                  Cancel
                </Button>
                <Button
                  variant="primary"
                  onClick={handleExport}
                  className="flex items-center gap-3 px-6 py-3 bg-blue-600 dark:bg-blue-500 hover:bg-blue-700 dark:hover:bg-blue-600 text-white shadow-lg font-medium"
                >
                  <Download className="w-5 h-5" />
                  Export Data
                </Button>
              </div>
            </div>
          </div>
        </FocusModal.Body>
      </FocusModal.Content>
    </FocusModal>
  );
};

export default ExportModal;
