import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "@camped-ai/ui";
import { sdk } from "../../lib/sdk";

export interface Tag {
  id: string;
  name: string;
  color?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface CreateTagInput {
  name: string;
  color?: string;
  is_active?: boolean;
}

export interface UpdateTagInput {
  name?: string;
  color?: string;
  is_active?: boolean;
}

export interface TagFilters {
  name?: string;
  is_active?: boolean;
  limit?: number;
  offset?: number;
}

export interface TagListResponse {
  tags: Tag[];
  count: number;
  limit: number;
  offset: number;
}

const QUERY_KEYS = {
  all: ["supplier-products-services", "tags"] as const,
  lists: () => [...QUERY_KEYS.all, "list"] as const,
  list: (filters: TagFilters) => [...QUERY_KEYS.lists(), filters] as const,
  details: () => [...QUERY_KEYS.all, "detail"] as const,
  detail: (id: string) => [...QUERY_KEYS.details(), id] as const,
};

export const useTags = (filters: TagFilters = {}) => {
  return useQuery({
    queryKey: QUERY_KEYS.list(filters),
    queryFn: async (): Promise<TagListResponse> => {
      const params = new URLSearchParams();

      if (filters.name) params.append("name", filters.name);
      if (filters.is_active !== undefined)
        params.append("is_active", filters.is_active.toString());
      if (filters.limit) params.append("limit", filters.limit.toString());
      if (filters.offset) params.append("offset", filters.offset.toString());

      try {
        const result = (await sdk.client.fetch(
          `/admin/supplier-management/products-services/tags?${params.toString()}`
        )) as any;
        return result;
      } catch (error) {
        throw error;
      }
    },
  });
};

export const useTag = (id: string) => {
  return useQuery({
    queryKey: QUERY_KEYS.detail(id),
    queryFn: async (): Promise<{ tag: Tag }> => {
      try {
        const result = (await sdk.client.fetch(
          `/admin/supplier-management/products-services/tags/${id}`
        )) as any;
        return result;
      } catch (error) {
        throw error;
      }
    },
    enabled: !!id,
  });
};

export const useCreateTag = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateTagInput): Promise<{ tag: Tag }> => {
      console.log("Creating tag with data:", data);

      try {
        const result = (await sdk.client.fetch(
          "/admin/supplier-management/products-services/tags",
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(data),
          }
        )) as any;

        console.log("Create tag success:", result);
        return result;
      } catch (error) {
        console.error("Create tag error:", error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.lists() });
      toast.success("Tag created successfully");
    },
    onError: (error: Error) => {
      console.error("Create tag mutation error:", error);
      toast.error(error.message);
    },
  });
};

export const useUpdateTag = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      id,
      data,
    }: {
      id: string;
      data: UpdateTagInput;
    }): Promise<{ tag: Tag }> => {
      console.log("Updating tag with data:", { id, data });

      try {
        const result = (await sdk.client.fetch(
          `/admin/supplier-management/products-services/tags/${id}`,
          {
            method: "PUT",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(data),
          }
        )) as any;

        console.log("Update tag success:", result);
        return result;
      } catch (error) {
        console.error("Update tag error:", error);
        throw error;
      }
    },
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.detail(id) });
      toast.success("Tag updated successfully");
    },
    onError: (error: Error) => {
      console.error("Update tag mutation error:", error);
      toast.error(error.message);
    },
  });
};

export const useDeleteTag = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (
      id: string
    ): Promise<{ deleted: boolean; id: string }> => {
      console.log("Deleting tag with id:", id);

      try {
        const result = (await sdk.client.fetch(
          `/admin/supplier-management/products-services/tags/${id}`,
          {
            method: "DELETE",
            headers: {
              "Content-Type": "application/json",
            },
          }
        )) as any;

        console.log("Delete tag success:", result);
        return result;
      } catch (error) {
        console.error("Delete tag error:", error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.lists() });
      toast.success("Tag deleted successfully");
    },
    onError: (error: Error) => {
      console.error("Delete tag mutation error:", error);
      toast.error(error.message);
    },
  });
};
