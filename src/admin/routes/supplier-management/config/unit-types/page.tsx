import { <PERSON><PERSON><PERSON>, <PERSON>rash, <PERSON><PERSON><PERSON><PERSON> } from "@camped-ai/icons";
import { Edit, MoreHorizontal } from "lucide-react";
import {
  Container,
  Heading,
  Text,
  Button,
  Input,
  Table,
  Badge,
  Toaster,
  toast,
  FocusModal,
  Label,
  Textarea,
  DropdownMenu,
  Prompt,
} from "@camped-ai/ui";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import PermissionBasedSidebarHider from "../../../../widgets/permission-based-sidebar-hider";
import {
  useUnitTypes,
  useCreateUnitType,
  useUpdateUnitType,
  useDeleteUnitType,
  type UnitType,
  type CreateUnitTypeInput,
  type UpdateUnitTypeInput,
} from "../../../../hooks/supplier-products-services/use-unit-types";

interface FormData {
  name: string;
  description: string;
  is_active: boolean;
}

const UnitTypesConfigPage = () => {
  const navigate = useNavigate();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingUnitType, setEditingUnitType] = useState<UnitType | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [formData, setFormData] = useState<FormData>({
    name: "",
    description: "",
    is_active: true,
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [unitTypeToDelete, setUnitTypeToDelete] = useState<UnitType | null>(
    null
  );

  // Hooks
  const { data: unitTypesData, isLoading } = useUnitTypes({
    name: searchTerm || undefined,
    limit: 50,
  });
  const createUnitType = useCreateUnitType();
  const updateUnitType = useUpdateUnitType();
  const deleteUnitType = useDeleteUnitType();

  const unitTypes = unitTypesData?.unit_types || [];

  const handleInputChange = (field: keyof FormData, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = "Name is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      toast.error("Please fix the errors before submitting");
      return;
    }

    try {
      const data = {
        name: formData.name.trim(),
        description: formData.description.trim() || undefined,
        is_active: formData.is_active,
      };

      if (editingUnitType) {
        await updateUnitType.mutateAsync({
          id: editingUnitType.id,
          data: data as UpdateUnitTypeInput,
        });
        toast.success("Unit type updated successfully");
      } else {
        await createUnitType.mutateAsync(data as CreateUnitTypeInput);
        toast.success("Unit type created successfully");
      }

      closeModal();
    } catch (error) {
      console.error("Error saving unit type:", error);
    }
  };

  const handleEdit = (unitType: UnitType) => {
    setEditingUnitType(unitType);
    setFormData({
      name: unitType.name,
      description: unitType.description || "",
      is_active: unitType.is_active,
    });
    setIsModalOpen(true);
  };

  const handleDelete = (unitType: UnitType) => {
    setUnitTypeToDelete(unitType);
    setDeleteConfirmOpen(true);
  };

  const confirmDelete = async () => {
    if (!unitTypeToDelete) return;

    try {
      await deleteUnitType.mutateAsync(unitTypeToDelete.id);
      toast.success("Unit type deleted successfully");
      setDeleteConfirmOpen(false);
      setUnitTypeToDelete(null);
    } catch (error) {
      console.error("Error deleting unit type:", error);
      toast.error("Failed to delete unit type");
    }
  };

  const cancelDelete = () => {
    setDeleteConfirmOpen(false);
    setUnitTypeToDelete(null);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setEditingUnitType(null);
    setFormData({
      name: "",
      description: "",
      is_active: true,
    });
    setErrors({});
  };

  const openCreateModal = () => {
    setEditingUnitType(null);
    setFormData({
      name: "",
      description: "",
      is_active: true,
    });
    setIsModalOpen(true);
  };

  return (
    <>
      <PermissionBasedSidebarHider />
      <Container className="divide-y p-0">
        {/* Breadcrumb Navigation */}
        <div className="px-6 py-3 bg-ui-bg-subtle border-b border-ui-border-base">
          <div className="flex items-center gap-2 text-sm">
            <button
              onClick={() => navigate("/supplier-management")}
              className="flex items-center gap-2 text-ui-fg-interactive hover:text-ui-fg-interactive-hover font-medium transition-colors"
            >
              <ArrowLeft className="h-4 w-4" />
              Supplier Management
            </button>
            <span className="text-ui-fg-muted">/</span>
            <span className="text-ui-fg-subtle">Configuration</span>
            <span className="text-ui-fg-muted">/</span>
            <span className="text-ui-fg-base font-medium">Unit Types</span>
          </div>
        </div>

        {/* Header */}
        <div className="flex items-center justify-between px-6 py-4">
          <div>
            <Heading level="h2">Unit Types Configuration</Heading>
            <Text className="text-ui-fg-subtle">
              Manage how products and services are measured or billed
            </Text>
          </div>
          <Button size="small" onClick={openCreateModal}>
            <PlusMini />
            Add Unit Type
          </Button>
        </div>

        {/* Search */}
        <div className="px-6 py-4">
          <Input
            placeholder="Search unit types..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="max-w-md"
          />
        </div>

        {/* Table */}
        <div className="overflow-x-auto">
          <Table>
            <Table.Header>
              <Table.Row>
                <Table.HeaderCell>Name</Table.HeaderCell>
                <Table.HeaderCell>Description</Table.HeaderCell>
                <Table.HeaderCell>Status</Table.HeaderCell>
                <Table.HeaderCell>Actions</Table.HeaderCell>
              </Table.Row>
            </Table.Header>
            <Table.Body>
              {isLoading ? (
                Array.from({ length: 5 }).map((_, index) => (
                  <Table.Row key={`skeleton-${index}`}>
                    <Table.Cell>
                      <div className="h-4 bg-ui-bg-subtle rounded animate-pulse" />
                    </Table.Cell>
                    <Table.Cell>
                      <div className="h-4 bg-ui-bg-subtle rounded animate-pulse w-32" />
                    </Table.Cell>
                    <Table.Cell>
                      <div className="h-4 bg-ui-bg-subtle rounded animate-pulse w-16" />
                    </Table.Cell>
                    <Table.Cell>
                      <div className="h-4 bg-ui-bg-subtle rounded animate-pulse w-8" />
                    </Table.Cell>
                  </Table.Row>
                ))
              ) : unitTypes.length === 0 ? (
                <Table.Row>
                  <Table.Cell
                    className="text-center py-8"
                    style={{ gridColumn: "1 / -1" }}
                  >
                    <Text className="text-ui-fg-subtle">
                      {searchTerm
                        ? "No unit types found matching your search"
                        : "No unit types found"}
                    </Text>
                  </Table.Cell>
                </Table.Row>
              ) : (
                unitTypes.map((unitType) => (
                  <Table.Row key={unitType.id}>
                    <Table.Cell>
                      <Text weight="plus">{unitType.name}</Text>
                    </Table.Cell>
                    <Table.Cell>
                      <Text className="text-ui-fg-subtle">
                        {unitType.description || "—"}
                      </Text>
                    </Table.Cell>
                    <Table.Cell>
                      <Badge
                        className={`
                          inline-flex items-center px-3 py-1 text-xs font-medium rounded-full border transition-colors
                          ${
                            unitType.is_active
                              ? "bg-green-50 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800"
                              : "bg-gray-50 text-gray-600 border-gray-200 dark:bg-ui-bg-subtle dark:text-ui-fg-subtle dark:border-ui-border-base"
                          }
                        `}
                      >
                        {unitType.is_active ? "Active" : "Inactive"}
                      </Badge>
                    </Table.Cell>
                    <Table.Cell>
                      <DropdownMenu>
                        <DropdownMenu.Trigger asChild>
                          <Button variant="transparent" size="small">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenu.Trigger>
                        <DropdownMenu.Content align="end">
                          <DropdownMenu.Item
                            onClick={() => handleEdit(unitType)}
                          >
                            <Edit className="h-4 w-4" />
                            Edit
                          </DropdownMenu.Item>
                          <DropdownMenu.Item
                            onClick={() => handleDelete(unitType)}
                            className="text-red-600"
                          >
                            <Trash className="h-4 w-4" />
                            Delete
                          </DropdownMenu.Item>
                        </DropdownMenu.Content>
                      </DropdownMenu>
                    </Table.Cell>
                  </Table.Row>
                ))
              )}
            </Table.Body>
          </Table>
        </div>
      </Container>

      {/* Create/Edit Modal */}
      <FocusModal open={isModalOpen} onOpenChange={setIsModalOpen}>
        <FocusModal.Content>
          <FocusModal.Header>
            <Heading level="h2">
              {editingUnitType ? "Edit Unit Type" : "Add New Unit Type"}
            </Heading>
          </FocusModal.Header>

          <form onSubmit={handleSubmit}>
            <FocusModal.Body className="space-y-4">
              <div>
                <Label htmlFor="name">Name *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                  placeholder="e.g., Per Person, Per Day, Per Trip"
                  className={errors.name ? "border-red-500" : ""}
                />
                {errors.name && (
                  <Text size="small" className="text-red-600 mt-1">
                    {errors.name}
                  </Text>
                )}
              </div>

              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) =>
                    handleInputChange("description", e.target.value)
                  }
                  placeholder="Enter description (optional)"
                  rows={3}
                />
              </div>

              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="is_active"
                  checked={formData.is_active}
                  onChange={(e) =>
                    handleInputChange("is_active", e.target.checked)
                  }
                  className="rounded border-gray-300"
                />
                <Label htmlFor="is_active">Active</Label>
              </div>
            </FocusModal.Body>

            <FocusModal.Footer>
              <div className="flex items-center gap-2">
                <Button type="button" variant="secondary" onClick={closeModal}>
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={
                    createUnitType.isPending || updateUnitType.isPending
                  }
                >
                  {editingUnitType ? "Update" : "Create"}
                </Button>
              </div>
            </FocusModal.Footer>
          </form>
        </FocusModal.Content>
      </FocusModal>

      {/* Delete Confirmation Prompt */}
      <Prompt open={deleteConfirmOpen} onOpenChange={setDeleteConfirmOpen}>
        <Prompt.Content>
          <Prompt.Header>
            <Prompt.Title>Delete Unit Type</Prompt.Title>
            <Prompt.Description>
              Are you sure you want to delete "{unitTypeToDelete?.name}"? This
              action cannot be undone.
            </Prompt.Description>
          </Prompt.Header>
          <Prompt.Footer>
            <Prompt.Cancel onClick={cancelDelete}>Cancel</Prompt.Cancel>
            <Prompt.Action
              onClick={confirmDelete}
              disabled={deleteUnitType.isPending}
            >
              {deleteUnitType.isPending ? "Deleting..." : "Delete"}
            </Prompt.Action>
          </Prompt.Footer>
        </Prompt.Content>
      </Prompt>

      <Toaster />
    </>
  );
};

// Note: No route config export to prevent this from appearing as a top-level menu item
// This page is accessible through the Supplier Management dashboard

export default UnitTypesConfigPage;
