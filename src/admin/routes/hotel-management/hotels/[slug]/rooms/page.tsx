import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import { useState, useMemo } from "react";
import {
  Container,
  Heading,
  Text,
  Button,
  Toaster,
  toast,
  Badge,
  Input,
  Table,
  DropdownMenu,
  Select,
} from "@camped-ai/ui";
import { ChevronLeft } from "@camped-ai/icons";
import { defineRouteConfig } from "@camped-ai/admin-sdk";
import {
  Bed,
  Users,
  Home,
  Search,
  Eye,
  Map,
  Building2,
  MoreHorizontal,
  Clock,
} from "lucide-react";
import PermissionBasedSidebarHider from "../../../../../widgets/permission-based-sidebar-hider";
import { useRbac } from "../../../../../hooks/use-rbac";
import {
  useRoomsWithConfigs,
  type Room,
  type RoomConfig,
} from "../../../../../hooks/hotel-management";

interface Hotel {
  id: string;
  name: string;
  description?: string;
  address?: string;
  city?: string;
  country?: string;
  postal_code?: string;
  phone?: string;
  email?: string;
  website?: string;
  destination_id?: string;
  category_id?: string;
  images?: any[];
}

const HotelRoomsOverviewPage = () => {
  const { slug } = useParams();
  const navigate = useNavigate();
  const { hasPermission } = useRbac();

  // State for filters
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedRoomConfig, setSelectedRoomConfig] = useState<string>("all");
  const [selectedFloor, setSelectedFloor] = useState<string>("all");

  // Use TanStack Query to fetch rooms and room configs
  const { rooms, roomConfigs, isLoading, error } = useRoomsWithConfigs(
    slug || "",
    selectedRoomConfig
  );

  // Mock hotel data (you can replace this with actual hotel fetching)
  const hotel: Hotel = useMemo(
    () => ({
      id: slug || "hotel_mock",
      name: "Sample Hotel",
      description: "A beautiful hotel in the heart of the city",
      address: "123 Main St",
      city: "New York",
      country: "USA",
      postal_code: "10001",
      phone: "************",
      email: "<EMAIL>",
      website: "https://samplehotel.com",
      destination_id: "destination_1",
      category_id: "category_1",
      images: [],
    }),
    [slug]
  );

  // Handle error display
  if (error) {
    toast.error("Error", {
      description: "Failed to load rooms data",
    });
  }

  // Helper function to get room config for a room
  const getRoomConfig = (roomConfigId: string): RoomConfig | undefined => {
    return roomConfigs.find((config) => config.id === roomConfigId);
  };

  // Enhanced helper function to get room config from room data (includes embedded config)
  const getRoomConfigFromRoom = (room: Room): RoomConfig | undefined => {
    // First try to use the embedded room_config data from the API
    if (room.room_config) {
      return room.room_config;
    }
    // Fallback to the separate roomConfigs array
    return getRoomConfig(room.room_config_id);
  };

  // Get unique floors for filter
  const uniqueFloors = useMemo(
    () => Array.from(new Set(rooms.map((room) => room.floor).filter(Boolean))),
    [rooms]
  );

  // Filter rooms based on search and filters
  const filteredRooms = useMemo(() => {
    return rooms.filter((room) => {
      const roomConfig = getRoomConfigFromRoom(room);
      const searchLower = searchQuery.toLowerCase();

      const matchesSearch =
        room.name.toLowerCase().includes(searchLower) ||
        room.room_number.toLowerCase().includes(searchLower) ||
        room.floor.toLowerCase().includes(searchLower) ||
        room.notes.toLowerCase().includes(searchLower) ||
        roomConfig?.name.toLowerCase().includes(searchLower) ||
        roomConfig?.type.toLowerCase().includes(searchLower);

      const matchesRoomConfig =
        selectedRoomConfig === "all" ||
        room.room_config_id === selectedRoomConfig;

      const matchesFloor =
        selectedFloor === "all" || room.floor === selectedFloor;

      return matchesSearch && matchesRoomConfig && matchesFloor;
    });
  }, [rooms, searchQuery, selectedRoomConfig, selectedFloor]);

  return (
    <>
      <PermissionBasedSidebarHider />
      <Toaster />
      <div className="space-y-6">
        <Container className="py-6">
          {/* Header with Back Button and Title */}
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
            <div className="flex items-center gap-3">
              <Button
                variant="secondary"
                size="small"
                onClick={() => navigate(`/hotel-management/hotels/${slug}`)}
                className="rounded-full h-9 w-9 p-0 flex items-center justify-center shadow-sm"
              >
                <ChevronLeft />
              </Button>
              <div>
                <Heading level="h1" className="text-2xl font-bold">
                  All Hotel Rooms
                </Heading>
                <Text className="text-gray-500 dark:text-gray-400 text-sm">
                  Complete overview of all rooms for{" "}
                  {hotel?.name || "this hotel"}
                </Text>
              </div>
            </div>
          </div>
        </Container>

        {/* Main Content */}
        <div className="flex-1 px-8 pb-8">
          <Container className="py-6">
            {/* Header Section */}
            <div className="flex flex-col gap-4 mb-6"></div>

            {/* Stats Summary */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                <div className="flex items-center justify-between">
                  <div>
                    <Text className="text-gray-500 dark:text-gray-400 text-sm">
                      Total Rooms
                    </Text>
                    <Heading
                      level="h3"
                      className="text-2xl font-bold dark:text-gray-200"
                    >
                      {rooms.length}
                    </Heading>
                  </div>
                  <div className="bg-blue-50 dark:bg-blue-900/30 p-3 rounded-full">
                    <Building2 className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                  </div>
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                <div className="flex items-center justify-between">
                  <div>
                    <Text className="text-gray-500 dark:text-gray-400 text-sm">
                      Room Types
                    </Text>
                    <Heading
                      level="h3"
                      className="text-2xl font-bold dark:text-gray-200"
                    >
                      {roomConfigs.length}
                    </Heading>
                  </div>
                  <div className="bg-green-50 dark:bg-green-900/30 p-3 rounded-full">
                    <Bed className="w-6 h-6 text-green-600 dark:text-green-400" />
                  </div>
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                <div className="flex items-center justify-between">
                  <div>
                    <Text className="text-gray-500 dark:text-gray-400 text-sm">
                      Total Capacity
                    </Text>
                    <Heading
                      level="h3"
                      className="text-2xl font-bold dark:text-gray-200"
                    >
                      {rooms.reduce((sum, room) => {
                        const roomConfig = getRoomConfigFromRoom(room);
                        return sum + (roomConfig?.max_occupancy || 0);
                      }, 0)}{" "}
                      guests
                    </Heading>
                  </div>
                  <div className="bg-purple-50 dark:bg-purple-900/30 p-3 rounded-full">
                    <Users className="w-6 h-6 text-purple-600 dark:text-purple-400" />
                  </div>
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                <div className="flex items-center justify-between">
                  <div>
                    <Text className="text-gray-500 dark:text-gray-400 text-sm">
                      Floors
                    </Text>
                    <Heading
                      level="h3"
                      className="text-2xl font-bold dark:text-gray-200"
                    >
                      {uniqueFloors.length}
                    </Heading>
                  </div>
                  <div className="bg-amber-50 dark:bg-amber-900/30 p-3 rounded-full">
                    <Home className="w-6 h-6 text-amber-600 dark:text-amber-400" />
                  </div>
                </div>
              </div>
            </div>

            {/* Search and Filters */}
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 mb-6">
              <div className="flex flex-col lg:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Input
                      placeholder="Search rooms by number, name, floor, or room type..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-9 h-10 rounded-lg border-gray-300 dark:border-gray-600"
                    />
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 dark:text-gray-500" />
                  </div>
                </div>

                <div className="flex gap-2">
                  <Select
                    value={selectedRoomConfig}
                    onValueChange={setSelectedRoomConfig}
                  >
                    <Select.Trigger className="w-48">
                      <Select.Value placeholder="All Room Types" />
                    </Select.Trigger>
                    <Select.Content>
                      <Select.Item value="all">All Room Types</Select.Item>
                      {roomConfigs.map((config) => (
                        <Select.Item key={config.id} value={config.id}>
                          {config.name}
                        </Select.Item>
                      ))}
                    </Select.Content>
                  </Select>

                  <Select
                    value={selectedFloor}
                    onValueChange={setSelectedFloor}
                  >
                    <Select.Trigger className="w-48">
                      <Select.Value placeholder="All Floors" />
                    </Select.Trigger>
                    <Select.Content>
                      <Select.Item value="all">All Floors</Select.Item>
                      {uniqueFloors.map((floor) => (
                        <Select.Item key={floor} value={floor}>
                          Floor {floor}
                        </Select.Item>
                      ))}
                    </Select.Content>
                  </Select>

                  {(searchQuery ||
                    selectedRoomConfig !== "all" ||
                    selectedFloor !== "all") && (
                    <Button
                      variant="secondary"
                      size="small"
                      onClick={() => {
                        setSearchQuery("");
                        setSelectedRoomConfig("all");
                        setSelectedFloor("all");
                      }}
                      className="h-10"
                    >
                      Clear Filters
                    </Button>
                  )}
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-2 mb-6">
              {hasPermission("rooms:view") && (
                <Button
                  variant="secondary"
                  onClick={() =>
                    navigate(`/hotel-management/hotels/${slug}/room-configs`)
                  }
                  className="flex items-center gap-2"
                >
                  <Bed className="w-4 h-4" />
                  Manage Room Types
                </Button>
              )}
              {hasPermission("rooms:availability") && (
                <Button
                  variant="secondary"
                  onClick={() =>
                    navigate(
                      `/hotel-management/hotels/${slug}/availability-new`
                    )
                  }
                  className="flex items-center gap-2"
                >
                  <Clock className="w-4 h-4" />
                  Manage Availability
                </Button>
              )}
              {hasPermission("rooms:view") && (
                <Button
                  variant="secondary"
                  onClick={() =>
                    navigate(`/hotel-management/hotels/${slug}/floor-plan`)
                  }
                  className="flex items-center gap-2"
                >
                  <Map className="w-4 h-4" />
                  Floor Plan
                </Button>
              )}
            </div>

            {/* Rooms Display */}
            {isLoading ? (
              <div className="animate-pulse space-y-4">
                {[1, 2, 3, 4, 5].map((i) => (
                  <div key={i} className="bg-muted h-32 rounded-lg"></div>
                ))}
              </div>
            ) : filteredRooms.length === 0 ? (
              <div className="bg-card border border-border rounded-lg p-8 text-center">
                <div className="flex flex-col items-center justify-center py-8">
                  <div className="bg-muted p-4 rounded-full mb-4">
                    {searchQuery ||
                    selectedRoomConfig !== "all" ||
                    selectedFloor !== "all" ? (
                      <Search className="w-8 h-8 text-blue-600" />
                    ) : (
                      <Building2 className="w-8 h-8 text-blue-600" />
                    )}
                  </div>
                  <Heading
                    level="h3"
                    className="text-xl font-semibold mb-2 text-card-foreground"
                  >
                    {searchQuery ||
                    selectedRoomConfig !== "all" ||
                    selectedFloor !== "all"
                      ? "No matching rooms found"
                      : "No rooms found"}
                  </Heading>
                  <Text className="text-muted-foreground mb-6 max-w-md">
                    {searchQuery ||
                    selectedRoomConfig !== "all" ||
                    selectedFloor !== "all"
                      ? "Try adjusting your search criteria or filters."
                      : "No rooms have been created for this hotel yet. Start by creating room configurations and then add individual rooms."}
                  </Text>
                  {!(
                    searchQuery ||
                    selectedRoomConfig !== "all" ||
                    selectedFloor !== "all"
                  ) &&
                    hasPermission("rooms:create") && (
                      <Button
                        variant="primary"
                        onClick={() =>
                          navigate(
                            `/hotel-management/hotels/${slug}/room-configs`
                          )
                        }
                        className="rounded-lg shadow-sm"
                      >
                        <Bed className="w-4 h-4 mr-2" />
                        Create Room Configurations
                      </Button>
                    )}
                </div>
              </div>
            ) : (
              <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
                <div className="border-b border-gray-200 dark:border-gray-700 px-5 py-4">
                  <Heading
                    level="h2"
                    className="text-lg font-semibold dark:text-gray-200"
                  >
                    Rooms ({filteredRooms.length})
                  </Heading>
                </div>

                <div className="overflow-x-auto">
                  <Table>
                    <Table.Header>
                      <Table.Row>
                        <Table.HeaderCell>Room Details</Table.HeaderCell>
                        <Table.HeaderCell>Room Type</Table.HeaderCell>
                        <Table.HeaderCell>Capacity</Table.HeaderCell>
                        {/* <Table.HeaderCell>Amenities</Table.HeaderCell> */}
                        <Table.HeaderCell>Floor</Table.HeaderCell>
                        {/* <Table.HeaderCell>Notes</Table.HeaderCell> */}
                        <Table.HeaderCell>Actions</Table.HeaderCell>
                      </Table.Row>
                    </Table.Header>
                    <Table.Body>
                      {filteredRooms.map((room) => {
                        const roomConfig = getRoomConfigFromRoom(room);
                        return (
                          <Table.Row key={room.id}>
                            <Table.Cell>
                              <div className="flex items-center gap-3">
                                <div className="bg-blue-50 dark:bg-blue-900/30 p-2 rounded-lg">
                                  <Bed className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                                </div>
                                <div>
                                  <Text className="font-medium text-gray-900 dark:text-gray-200">
                                    {room.room_number}
                                  </Text>
                                  <Text className="text-sm text-gray-500 dark:text-gray-400">
                                    {room.name}
                                  </Text>
                                </div>
                              </div>
                            </Table.Cell>

                            <Table.Cell>
                              <div className="space-y-1">
                                <Text className="text-xs text-muted-foreground block">
                                  {roomConfig?.name || "Unknown Configuration"}
                                </Text>
                              </div>
                            </Table.Cell>

                            <Table.Cell>
                              <div className="space-y-1">
                                <div className="flex items-center gap-1">
                                  <Users className="w-4 h-4 text-muted-foreground" />
                                  <Text className="text-sm font-medium text-card-foreground">
                                    {roomConfig?.max_occupancy || 0} guests
                                  </Text>
                                </div>
                                <Text className="text-xs text-muted-foreground">
                                  {roomConfig?.max_adults || 0} adults,{" "}
                                  {roomConfig?.max_children || 0} children
                                </Text>
                              </div>
                            </Table.Cell>

                            {/* <Table.Cell>
                            <div className="space-y-1">
                              {roomConfig?.amenities && roomConfig.amenities.length > 0 ? (
                                roomConfig.amenities.length <= 2 ? (
                                  <Text className="text-sm">
                                    {roomConfig.amenities.join(", ")}
                                  </Text>
                                ) : (
                                  <Tooltip content={roomConfig.amenities.join(", ")}>
                                    <div className="flex items-center gap-1 cursor-help">
                                      <Coffee className="w-4 h-4 text-gray-400" />
                                      <Text className="text-sm">
                                        {roomConfig.amenities.length} amenities
                                      </Text>
                                    </div>
                                  </Tooltip>
                                )
                              ) : (
                                <Text className="text-sm text-gray-400">None</Text>
                              )}
                              {roomConfig?.bed_type && (
                                <Text className="text-xs text-gray-500">
                                  {roomConfig.bed_type.charAt(0).toUpperCase() +
                                   roomConfig.bed_type.slice(1)} bed
                                </Text>
                              )}
                            </div>
                          </Table.Cell> */}

                            <Table.Cell>
                              <Badge color="grey" className="font-normal">
                                Floor {room.floor || "N/A"}
                              </Badge>
                            </Table.Cell>

                            {/* <Table.Cell>
                            <Text className="text-sm text-gray-600 truncate max-w-[150px]">
                              {room.notes || "-"}
                            </Text>
                          </Table.Cell> */}

                            <Table.Cell>
                              <DropdownMenu>
                                <DropdownMenu.Trigger asChild>
                                  <Button
                                    variant="secondary"
                                    size="small"
                                    className="h-8 w-8 p-0"
                                  >
                                    <MoreHorizontal className="w-4 h-4" />
                                  </Button>
                                </DropdownMenu.Trigger>
                                <DropdownMenu.Content align="end">
                                  {hasPermission("rooms:view") && (
                                    <DropdownMenu.Item
                                      onClick={() =>
                                        navigate(
                                          `/hotel-management/hotels/${slug}/room-configs/${room.room_config_id}/rooms`
                                        )
                                      }
                                    >
                                      <Eye className="w-4 h-4 mr-2" />
                                      View Room Details
                                    </DropdownMenu.Item>
                                  )}
                                  {hasPermission("rooms:availability") && (
                                    <DropdownMenu.Item
                                      onClick={() =>
                                        navigate(
                                          `/hotel-management/hotels/${slug}/availability?room=${room.id}`
                                        )
                                      }
                                    >
                                      <Clock className="w-4 h-4 mr-2" />
                                      Manage Availability
                                    </DropdownMenu.Item>
                                  )}
                                  {(hasPermission("rooms:view") ||
                                    hasPermission("rooms:availability")) &&
                                    hasPermission("rooms:edit") && (
                                      <DropdownMenu.Separator />
                                    )}
                                  {hasPermission("rooms:edit") && (
                                    <DropdownMenu.Item
                                      onClick={() =>
                                        navigate(
                                          `/hotel-management/hotels/${slug}/room-configs/${room.room_config_id}`
                                        )
                                      }
                                    >
                                      <Bed className="w-4 h-4 mr-2" />
                                      Edit Room Type
                                    </DropdownMenu.Item>
                                  )}
                                </DropdownMenu.Content>
                              </DropdownMenu>
                            </Table.Cell>
                          </Table.Row>
                        );
                      })}
                    </Table.Body>
                  </Table>
                </div>
              </div>
            )}
          </Container>
        </div>
      </div>
    </>
  );
};

export const config = defineRouteConfig({
  label: "All Rooms",
  icon: Building2,
});

export default HotelRoomsOverviewPage;
