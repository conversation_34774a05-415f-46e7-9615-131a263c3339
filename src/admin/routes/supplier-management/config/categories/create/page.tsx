import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { ArrowLeft } from "@camped-ai/icons";
import {
  Container,
  Heading,
  Text,
  Button,
  Input,
  Textarea,
  Select,
  Label,
  Toaster,
  toast,
} from "@camped-ai/ui";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import PermissionBasedSidebarHider from "../../../../../widgets/permission-based-sidebar-hider";
import { useCreateCategory } from "../../../../../hooks/supplier-products-services/use-categories";
import DynamicFieldSchemaBuilder, {
  type DynamicFieldSchema,
} from "../../../../../components/supplier-management/dynamic-field-schema-builder";

interface FormData {
  name: string;
  description: string;
  category_type: "Product" | "Service" | "Both";
  icon: string;
  dynamic_field_schema: DynamicFieldSchema[];
  is_active: boolean;
}

const CreateCategoryPage = () => {
  const navigate = useNavigate();
  const createCategory = useCreateCategory();

  const [formData, setFormData] = useState<FormData>({
    name: "",
    description: "",
    category_type: "Both",
    icon: "",
    dynamic_field_schema: [],
    is_active: true,
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);

  const handleInputChange = (field: keyof FormData, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = "Name is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      toast.error("Please fix the errors before submitting");
      return;
    }

    setLoading(true);
    try {
      const data = {
        name: formData.name.trim(),
        description: formData.description.trim() || undefined,
        category_type: formData.category_type,
        icon: formData.icon.trim() || undefined,
        dynamic_field_schema: formData.dynamic_field_schema,
        is_active: formData.is_active,
      };

      await createCategory.mutateAsync(data);
      toast.success("Category created successfully");
      navigate("/supplier-management/config/categories");
    } catch (error) {
      console.error("Error creating category:", error);
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to create category. Please try again.";
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <PermissionBasedSidebarHider />
      <Container className="divide-y p-0">
        {/* Header */}
        <div className="flex items-center justify-between px-6 py-4">
          <div className="flex items-center gap-x-4">
            <Button
              variant="transparent"
              onClick={() => navigate("/supplier-management/config/categories")}
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div>
              <Heading level="h2">Add New Category</Heading>
              <Text className="text-ui-fg-subtle">
                Create a new product or service category
              </Text>
            </div>
          </div>
        </div>

        {/* Breadcrumb Navigation */}
        <div className="px-6 py-3 bg-gray-50 border-b">
          <div className="flex items-center gap-2 text-sm">
            <button
              onClick={() => navigate("/supplier-management")}
              className="text-blue-600 hover:text-blue-800 font-medium"
            >
              Supplier Management
            </button>
            <span className="text-gray-400">/</span>
            <span className="text-gray-600">Configuration</span>
            <span className="text-gray-400">/</span>
            <button
              onClick={() => navigate("/supplier-management/config/categories")}
              className="text-blue-600 hover:text-blue-800 font-medium"
            >
              Categories
            </button>
            <span className="text-gray-400">/</span>
            <span className="text-gray-900 font-medium">Create</span>
          </div>
        </div>

        {/* Form */}
        <div className="px-4 md:px-6 py-6">
          <form
            onSubmit={handleSubmit}
            className="max-w-4xl space-y-6 md:space-y-8"
          >
            {/* Basic Information Section */}
            <div className="space-y-4">
              <Heading level="h3">Basic Information</Heading>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">Name *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange("name", e.target.value)}
                    placeholder="e.g., Equipment, Guiding, Transportation"
                    className={errors.name ? "border-red-500" : ""}
                  />
                  {errors.name && (
                    <Text size="small" className="text-red-600 mt-1">
                      {errors.name}
                    </Text>
                  )}
                </div>

                <div>
                  <Label htmlFor="category_type">Category Type</Label>
                  <Select
                    value={formData.category_type}
                    onValueChange={(value) =>
                      handleInputChange("category_type", value)
                    }
                  >
                    <Select.Trigger>
                      <Select.Value />
                    </Select.Trigger>
                    <Select.Content>
                      <Select.Item value="Product">Product</Select.Item>
                      <Select.Item value="Service">Service</Select.Item>
                      <Select.Item value="Both">Both</Select.Item>
                    </Select.Content>
                  </Select>
                </div>
              </div>

              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) =>
                    handleInputChange("description", e.target.value)
                  }
                  placeholder="Enter description (optional)"
                  rows={3}
                />
              </div>

              <div>
                <Label htmlFor="icon">Icon</Label>
                <Input
                  id="icon"
                  value={formData.icon}
                  onChange={(e) => handleInputChange("icon", e.target.value)}
                  placeholder="e.g., 🏷️, 📦, 🎯 (emoji or icon name)"
                />
                <Text size="small" className="text-ui-fg-subtle mt-1">
                  Optional icon for visual identification in the UI
                </Text>
              </div>

              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="is_active"
                  checked={formData.is_active}
                  onChange={(e) =>
                    handleInputChange("is_active", e.target.checked)
                  }
                  className="rounded border-gray-300"
                />
                <Label htmlFor="is_active">Active</Label>
              </div>
            </div>

            {/* Dynamic Field Schema Section */}
            <div className="space-y-4">
              <Heading level="h3">Dynamic Field Configuration</Heading>
              <Text className="text-ui-fg-subtle">
                Define custom fields that will appear when creating
                products/services in this category
              </Text>

              <DynamicFieldSchemaBuilder
                value={formData.dynamic_field_schema}
                onChange={(fields) =>
                  handleInputChange("dynamic_field_schema", fields)
                }
                error={errors.dynamic_field_schema}
              />
            </div>

            {/* Actions */}
            <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 sm:gap-x-4 pt-6 border-t">
              <Button
                type="submit"
                loading={loading}
                disabled={loading}
                className="w-full sm:w-auto"
              >
                Create Category
              </Button>
              <Button
                type="button"
                variant="secondary"
                onClick={() =>
                  navigate("/supplier-management/config/categories")
                }
                className="w-full sm:w-auto"
              >
                Cancel
              </Button>
            </div>
          </form>
        </div>
      </Container>
      <Toaster />
    </>
  );
};

export default CreateCategoryPage;
