// Hotel Management Pagination Test Script
// Run this in the browser console on the hotel management page

console.log("🧪 Starting Hotel Management Pagination Test");

// Test helper functions
const wait = (ms) => new Promise(resolve => setTimeout(resolve, ms));

const getCurrentURL = () => window.location.href;

const getCurrentPage = () => {
  const urlParams = new URLSearchParams(window.location.search);
  return parseInt(urlParams.get('page') || '1', 10);
};

const hasFilters = () => {
  const urlParams = new URLSearchParams(window.location.search);
  return !!(
    urlParams.get('destination') ||
    urlParams.get('stars') ||
    urlParams.get('featured') ||
    urlParams.get('search')
  );
};

const clickPageButton = (pageNumber) => {
  const pageButton = document.querySelector(`button[aria-label="Go to page ${pageNumber}"], button:contains("${pageNumber}")`);
  if (!pageButton) {
    // Try alternative selector
    const buttons = Array.from(document.querySelectorAll('button'));
    const targetButton = buttons.find(btn => btn.textContent.trim() === pageNumber.toString());
    if (targetButton) {
      targetButton.click();
      return true;
    }
    console.error(`❌ Could not find page ${pageNumber} button`);
    return false;
  }
  pageButton.click();
  return true;
};

const applyDestinationFilter = () => {
  // Find and click the destination dropdown
  const destinationSelect = document.querySelector('select[name="destination"], [data-testid="destination-select"]');
  if (destinationSelect && destinationSelect.options.length > 1) {
    destinationSelect.selectedIndex = 1; // Select first non-empty option
    destinationSelect.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }
  console.warn("⚠️ Could not find destination filter");
  return false;
};

// Test scenarios
const testScenarios = [
  {
    name: "Basic Pagination Without Filters",
    async run() {
      console.log("📋 Test: Basic Pagination Without Filters");
      
      // Ensure we're on page 1 with no filters
      window.history.pushState({}, '', '/app/hotel-management/hotels?page=1');
      await wait(1000);
      
      const initialPage = getCurrentPage();
      console.log(`Initial page: ${initialPage}`);
      
      // Click page 2
      if (clickPageButton(2)) {
        await wait(2000);
        const newPage = getCurrentPage();
        const success = newPage === 2;
        console.log(`${success ? '✅' : '❌'} Page 2 navigation: Expected 2, Got ${newPage}`);
        return success;
      }
      return false;
    }
  },
  
  {
    name: "Pagination With Filters Applied",
    async run() {
      console.log("📋 Test: Pagination With Filters Applied");
      
      // Apply a filter first
      window.history.pushState({}, '', '/app/hotel-management/hotels?page=1&destination=test');
      await wait(1000);
      
      const hasFiltersApplied = hasFilters();
      console.log(`Filters applied: ${hasFiltersApplied}`);
      
      if (!hasFiltersApplied) {
        console.warn("⚠️ No filters detected in URL");
      }
      
      // Click page 2
      if (clickPageButton(2)) {
        await wait(2000);
        const newPage = getCurrentPage();
        const stillHasFilters = hasFilters();
        const success = newPage === 2 && stillHasFilters;
        console.log(`${success ? '✅' : '❌'} Page 2 with filters: Expected page 2 with filters, Got page ${newPage}, filters: ${stillHasFilters}`);
        return success;
      }
      return false;
    }
  },
  
  {
    name: "Filter Change Should Reset to Page 1",
    async run() {
      console.log("📋 Test: Filter Change Should Reset to Page 1");
      
      // Start on page 2 with a filter
      window.history.pushState({}, '', '/app/hotel-management/hotels?page=2&destination=test1');
      await wait(1000);
      
      const initialPage = getCurrentPage();
      console.log(`Initial page: ${initialPage}`);
      
      // Change the filter
      window.history.pushState({}, '', '/app/hotel-management/hotels?page=2&destination=test2');
      await wait(2000);
      
      const newPage = getCurrentPage();
      const success = newPage === 1;
      console.log(`${success ? '✅' : '❌'} Filter change reset: Expected page 1, Got page ${newPage}`);
      return success;
    }
  }
];

// Run all tests
async function runAllTests() {
  console.log("🚀 Running all pagination tests...");
  
  const results = [];
  
  for (const scenario of testScenarios) {
    try {
      const result = await scenario.run();
      results.push({ name: scenario.name, passed: result });
      await wait(1000); // Wait between tests
    } catch (error) {
      console.error(`❌ Test "${scenario.name}" failed with error:`, error);
      results.push({ name: scenario.name, passed: false, error: error.message });
    }
  }
  
  // Summary
  console.log("\n📊 Test Results Summary:");
  console.log("========================");
  
  const passed = results.filter(r => r.passed).length;
  const total = results.length;
  
  results.forEach(result => {
    const status = result.passed ? '✅ PASS' : '❌ FAIL';
    console.log(`${status}: ${result.name}`);
    if (result.error) {
      console.log(`   Error: ${result.error}`);
    }
  });
  
  console.log(`\n🎯 Overall: ${passed}/${total} tests passed`);
  
  if (passed === total) {
    console.log("🎉 All tests passed! Pagination fix is working correctly.");
  } else {
    console.log("⚠️ Some tests failed. Please check the implementation.");
  }
  
  return results;
}

// Manual test functions
window.testPagination = {
  runAll: runAllTests,
  scenarios: testScenarios,
  helpers: {
    getCurrentURL,
    getCurrentPage,
    hasFilters,
    clickPageButton,
    applyDestinationFilter
  }
};

console.log("🧪 Pagination test script loaded!");
console.log("Run window.testPagination.runAll() to start testing");
console.log("Or run individual scenarios with window.testPagination.scenarios[0].run()");
