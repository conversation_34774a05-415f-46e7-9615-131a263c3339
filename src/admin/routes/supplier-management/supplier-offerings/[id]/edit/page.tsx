import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { ArrowLeft } from "@camped-ai/icons";
import { Save, X } from "lucide-react";
import {
  Container,
  Heading,
  Text,
  Button,
  Input,
  Textarea,
  Select,
  Label,
  Toaster,
  toast,
} from "@camped-ai/ui";
import { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import PermissionBasedSidebarHider from "../../../../../widgets/permission-based-sidebar-hider";
import {
  useSupplierOffering,
  useUpdateSupplierOffering,
} from "../../../../../hooks/supplier-products-services/use-supplier-offerings";
import { useSuppliers } from "../../../../../hooks/vendor-management/use-suppliers";
import { useProductsServices } from "../../../../../hooks/supplier-products-services/use-products-services";
import { useCategories } from "../../../../../hooks/supplier-products-services/use-categories";
import Dynamic<PERSON>ieldRenderer, {
  type DynamicFieldSchema,
} from "../../../../../components/supplier-management/dynamic-field-renderer";
import PricingCalculator from "../../../../../components/supplier-management/pricing-calculator";
import CostHistoryTable from "../../../../../components/supplier-management/cost-history-table";
import { useSupplierOfferingCostHistory } from "../../../../../hooks/supplier-products-services/use-supplier-offering-cost-history";
import { CURRENCIES } from "../../../../../constants/supplier-form-options";

interface FormData {
  supplier_id: string;
  product_service_id: string;
  active_from: string;
  active_to: string;
  availability_notes: string;

  // Legacy cost field
  cost: string;

  // Enhanced pricing fields
  commission: string;
  public_price: string;
  supplier_price: string;
  net_price: string;
  margin_rate: string;
  selling_price: string;
  custom_prices: Array<{ name: string; price: number }>;

  // Currency fields
  currency: string;
  currency_override: boolean;

  // Selling currency fields
  selling_currency: string;
  selling_price_selling_currency: string;
  exchange_rate: string;
  exchange_rate_date: string;

  status: "active" | "inactive";
  custom_fields: Record<string, any>;
  change_reason: string; // Optional reason for cost changes
}

const EditSupplierOfferingPage = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();

  // API calls
  const {
    data: offeringResponse,
    isLoading: offeringLoading,
    error,
  } = useSupplierOffering(id!);
  const { data: suppliersResponse, isLoading: suppliersLoading } =
    useSuppliers();
  const { data: productsServicesResponse, isLoading: productsServicesLoading } =
    useProductsServices();
  const { data: categoriesResponse } = useCategories();

  const updateSupplierOffering = useUpdateSupplierOffering();

  // Cost history data
  const { data: costHistoryResponse, isLoading: costHistoryLoading } =
    useSupplierOfferingCostHistory(id!, { limit: 10 });

  // Extract data
  const offering = offeringResponse?.supplier_offering;
  const suppliers = suppliersResponse?.suppliers || [];
  const productsServices = productsServicesResponse?.product_services || [];

  const [formData, setFormData] = useState<FormData>({
    supplier_id: "",
    product_service_id: "",
    active_from: "",
    active_to: "",
    availability_notes: "",

    // Legacy cost field
    cost: "",

    // Enhanced pricing fields
    commission: "",
    public_price: "",
    supplier_price: "",
    net_price: "",
    margin_rate: "",
    selling_price: "",
    custom_prices: [],

    // Currency fields
    currency: "CHF",
    currency_override: false,

    // Selling currency fields
    selling_currency: "CHF",
    selling_price_selling_currency: "",
    exchange_rate: "1.0",
    exchange_rate_date: "",

    status: "active",
    custom_fields: {},
    change_reason: "",
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [selectedProductService, setSelectedProductService] =
    useState<any>(null);
  const [selectedSupplier, setSelectedSupplier] = useState<any>(null);
  const [categorySchema, setCategorySchema] = useState<DynamicFieldSchema[]>(
    []
  );
  const [originalCost, setOriginalCost] = useState<string>("");
  const [showChangeReason, setShowChangeReason] = useState<boolean>(false);

  // Initialize form data when offering loads
  useEffect(() => {
    if (offering) {
      // Determine the currency to use based on override flag
      let currencyToUse = offering.currency || "CHF";
      const isOverride = offering.currency_override || false;

      // If not overridden and supplier has default currency, use supplier's default
      if (!isOverride && offering.supplier?.default_currency) {
        currencyToUse = offering.supplier.default_currency;
      }

      setFormData({
        supplier_id: offering.supplier_id,
        product_service_id: offering.product_service_id,
        active_from: offering.active_from
          ? offering.active_from.split("T")[0]
          : "",
        active_to: offering.active_to ? offering.active_to.split("T")[0] : "",
        availability_notes: offering.availability_notes || "",

        // Legacy cost field
        cost: offering.cost ? offering.cost.toString() : "",

        // Enhanced pricing fields
        commission: offering.commission
          ? (offering.commission * 100).toString()
          : "",
        public_price: offering.public_price
          ? offering.public_price.toString()
          : "",
        supplier_price: offering.supplier_price
          ? offering.supplier_price.toString()
          : "",
        net_price: offering.net_price ? offering.net_price.toString() : "",
        margin_rate: offering.margin_rate
          ? (offering.margin_rate * 100).toString()
          : "",
        selling_price: offering.selling_price
          ? offering.selling_price.toString()
          : "",
        custom_prices: offering.custom_prices || [],

        // Currency fields
        currency: currencyToUse,
        currency_override: isOverride,

        // Selling currency fields
        selling_currency: offering.selling_currency || currencyToUse,
        selling_price_selling_currency: offering.selling_price_selling_currency
          ? offering.selling_price_selling_currency.toString()
          : "",
        exchange_rate: offering.exchange_rate
          ? offering.exchange_rate.toString()
          : "1.0",
        exchange_rate_date: offering.exchange_rate_date
          ? offering.exchange_rate_date.toISOString().split("T")[0]
          : "",

        status: offering.status,
        custom_fields: offering.custom_fields || {},
        change_reason: "", // Reset change reason for new edits
      });

      // Set original cost for comparison
      setOriginalCost(offering.cost ? offering.cost.toString() : "");

      // Set selected supplier and product service
      if (offering.supplier) {
        setSelectedSupplier(offering.supplier);
      }
      if (offering.product_service) {
        setSelectedProductService(offering.product_service);
      }

      // Set category schema if available
      if (offering.product_service?.category?.dynamic_field_schema) {
        const offeringFields =
          offering.product_service.category.dynamic_field_schema.filter(
            (field: any) => field.used_in_supplier_offering
          );
        setCategorySchema(offeringFields);

        // Ensure locked fields have inherited values
        const updatedCustomFields = { ...offering.custom_fields };
        offeringFields.forEach((field: any) => {
          if (
            field.locked_in_offerings &&
            offering.product_service?.custom_fields?.[field.key] !== undefined
          ) {
            updatedCustomFields[field.key] =
              offering.product_service.custom_fields[field.key];
          }
        });

        // Update form data if there are any locked field updates
        if (
          JSON.stringify(updatedCustomFields) !==
          JSON.stringify(offering.custom_fields)
        ) {
          setFormData((prev) => ({
            ...prev,
            custom_fields: updatedCustomFields,
          }));
        }
      }
    }
  }, [offering]);

  // Update selected supplier and auto-fill currency when supplier_id changes
  useEffect(() => {
    if (formData.supplier_id) {
      const supplier = suppliers.find((s) => s.id === formData.supplier_id);
      setSelectedSupplier(supplier);

      // Auto-fill currency from supplier's default currency if not overridden
      if (supplier?.default_currency && !formData.currency_override) {
        setFormData((prev) => ({
          ...prev,
          currency: supplier.default_currency,
        }));
      }
    } else {
      setSelectedSupplier(null);
    }
  }, [formData.supplier_id, suppliers, formData.currency_override]);

  // Update selected product service and category schema when product_service_id changes
  useEffect(() => {
    if (formData.product_service_id) {
      const productService = productsServices.find(
        (ps) => ps.id === formData.product_service_id
      );
      setSelectedProductService(productService);

      if (productService?.category?.dynamic_field_schema) {
        // Filter fields that are used in supplier offerings
        const offeringFields =
          productService.category.dynamic_field_schema.filter(
            (field: any) => field.used_in_supplier_offering
          );
        setCategorySchema(offeringFields);

        // Initialize custom fields with default values and inherit locked fields
        const newCustomFields: Record<string, any> = {};

        offeringFields.forEach((field: any) => {
          if (
            field.locked_in_offerings &&
            productService.custom_fields?.[field.key] !== undefined
          ) {
            // Inherit value from product service for locked fields
            newCustomFields[field.key] =
              productService.custom_fields[field.key];
          } else if (formData.custom_fields[field.key] === undefined) {
            // Set default value for new fields
            newCustomFields[field.key] = field.default_value || "";
          } else {
            // Keep existing value
            newCustomFields[field.key] = formData.custom_fields[field.key];
          }
        });

        setFormData((prev) => ({
          ...prev,
          custom_fields: { ...prev.custom_fields, ...newCustomFields },
        }));
      } else {
        setCategorySchema([]);
      }
    } else {
      setSelectedProductService(null);
      setCategorySchema([]);
    }
  }, [formData.product_service_id, productsServices]);

  const handleInputChange = (field: keyof FormData, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));

    // Show change reason field when cost is modified
    if (field === "cost" && value !== originalCost) {
      setShowChangeReason(true);
    } else if (field === "cost" && value === originalCost) {
      setShowChangeReason(false);
      // Clear change reason when cost is reverted to original
      setFormData((prev) => ({ ...prev, change_reason: "" }));
    }

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  // Handle pricing calculator changes
  const handlePricingChange = (pricingData: any) => {
    setFormData((prev) => ({
      ...prev,
      commission: pricingData.commission
        ? (pricingData.commission * 100).toString()
        : "",
      public_price: pricingData.publicPrice?.toString() || "",
      supplier_price: pricingData.calculatedSupplierPrice?.toString() || "",
      net_price: pricingData.calculatedNetPrice?.toString() || "",
      margin_rate: pricingData.marginRate
        ? (pricingData.marginRate * 100).toString()
        : "",
      selling_price: pricingData.calculatedSellingPrice?.toString() || "",
      custom_prices: pricingData.customPrices || [],

      // Selling currency fields
      selling_currency: pricingData.sellingCurrency || prev.selling_currency,
      selling_price_selling_currency:
        pricingData.calculatedSellingPriceSellingCurrency?.toString() || "",
      exchange_rate: pricingData.exchangeRate?.toString() || prev.exchange_rate,
      exchange_rate_date: pricingData.exchangeRateDate
        ? pricingData.exchangeRateDate.toISOString().split("T")[0]
        : prev.exchange_rate_date,
    }));
  };

  const handleCustomFieldChange = (fieldKey: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      custom_fields: {
        ...prev.custom_fields,
        [fieldKey]: value,
      },
    }));
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.supplier_id) {
      newErrors.supplier_id = "Supplier is required";
    }

    if (!formData.product_service_id) {
      newErrors.product_service_id = "Product/Service is required";
    }

    if (!formData.cost || formData.cost.trim() === "") {
      newErrors.cost = "Cost is required";
    } else if (
      isNaN(parseFloat(formData.cost)) ||
      parseFloat(formData.cost) < 0
    ) {
      newErrors.cost = "Cost must be a valid positive number";
    }

    if (!formData.currency) {
      newErrors.currency = "Currency is required";
    }

    // Validate date range (only if both dates are provided)
    if (formData.active_from && formData.active_to) {
      const fromDate = new Date(formData.active_from);
      const toDate = new Date(formData.active_to);

      if (fromDate >= toDate) {
        newErrors.active_to = "Active To date must be after Active From date";
      }
    }

    // Note: Open-ended dates (empty active_to) are allowed and will be handled by server validation

    // Validate required custom fields (skip locked fields as they're inherited)
    categorySchema.forEach((field) => {
      if (
        field.required &&
        !field.locked_in_offerings &&
        !formData.custom_fields[field.key]
      ) {
        newErrors[`custom_field_${field.key}`] = `${field.label} is required`;
      }

      // Validate that locked fields haven't been modified
      if (field.locked_in_offerings && offering) {
        // For locked fields, the expected value should be from the product service
        const expectedValue =
          offering.product_service?.custom_fields?.[field.key];
        const currentValue = formData.custom_fields[field.key];

        // Only validate if the current value differs from the expected inherited value
        if (
          expectedValue !== undefined &&
          JSON.stringify(expectedValue) !== JSON.stringify(currentValue)
        ) {
          newErrors[
            `custom_field_${field.key}`
          ] = `${field.label} is locked and cannot be modified`;
        }
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      toast.error("Please fix the errors before saving");
      return;
    }

    try {
      // Filter out locked fields from custom_fields
      const filteredCustomFields = { ...formData.custom_fields };
      const lockedFields = categorySchema.filter(
        (field: any) => field.locked_in_offerings
      );

      // Remove locked fields from the update data
      lockedFields.forEach((field: any) => {
        delete filteredCustomFields[field.key];
      });

      const updateData = {
        custom_fields: filteredCustomFields,
        active_from: formData.active_from || undefined,
        active_to: formData.active_to || undefined,
        availability_notes: formData.availability_notes || undefined,

        // Legacy cost field
        cost: formData.cost ? parseFloat(formData.cost) : undefined,

        // Enhanced pricing fields
        commission: formData.commission
          ? parseFloat(formData.commission) / 100
          : undefined,
        public_price: formData.public_price
          ? parseFloat(formData.public_price)
          : undefined,
        supplier_price: formData.supplier_price
          ? parseFloat(formData.supplier_price)
          : undefined,
        net_price: formData.net_price
          ? parseFloat(formData.net_price)
          : undefined,
        margin_rate: formData.margin_rate
          ? parseFloat(formData.margin_rate) / 100
          : undefined,
        selling_price: formData.selling_price
          ? parseFloat(formData.selling_price)
          : undefined,
        custom_prices: formData.custom_prices,

        // Currency fields
        currency: formData.currency,
        currency_override: formData.currency_override,

        // Selling currency fields
        selling_currency: formData.selling_currency,
        selling_price_selling_currency: formData.selling_price_selling_currency
          ? parseFloat(formData.selling_price_selling_currency)
          : undefined,
        exchange_rate: formData.exchange_rate
          ? parseFloat(formData.exchange_rate)
          : undefined,
        exchange_rate_date: formData.exchange_rate_date
          ? new Date(formData.exchange_rate_date)
          : undefined,

        status: formData.status,

        // Only include change_reason if cost has actually changed
        change_reason: showChangeReason
          ? formData.change_reason || undefined
          : undefined,
      };

      await updateSupplierOffering.mutateAsync({ id: id!, data: updateData });
      navigate(`/supplier-management/supplier-offerings/${id}`);
    } catch (error: any) {
      console.error("Error updating supplier offering:", error);

      // Handle specific error types
      if (error?.response?.status === 409) {
        // Handle validation errors from the server
        let errorMessage =
          "This supplier offering configuration already exists.";

        if (error?.response?.data?.message) {
          errorMessage = error.response.data.message;
        } else if (error?.message) {
          errorMessage = error.message;
        }

        // Provide specific guidance for different types of conflicts
        if (errorMessage.includes("Date range conflict")) {
          // Already has specific date information
        } else if (errorMessage.includes("open-ended validity")) {
          // Already has specific open-ended conflict information
        } else if (errorMessage.includes("Cannot create offering")) {
          // Already has specific creation conflict information
        }

        toast.error(errorMessage);
      } else if (error?.response?.status === 400) {
        // Handle validation errors
        let errorMessage = "Invalid data provided.";

        if (error?.response?.data?.message) {
          errorMessage = error.response.data.message;
        } else if (error?.message) {
          errorMessage = error.message;
        }

        toast.error(errorMessage);
      } else {
        toast.error(error?.message || "Failed to update supplier offering");
      }
    }
  };

  if (offeringLoading) {
    return (
      <Container className="p-6">
        <div className="text-center">
          <Text>Loading supplier offering...</Text>
        </div>
      </Container>
    );
  }

  if (error || !offering) {
    return (
      <Container className="p-6">
        <div className="text-center">
          <Text className="text-ui-fg-error">
            {error?.message || "Supplier offering not found"}
          </Text>
          <Button
            variant="secondary"
            onClick={() => navigate("/supplier-management/supplier-offerings")}
            className="mt-4"
          >
            Back to Supplier Offerings
          </Button>
        </div>
      </Container>
    );
  }

  const isLoading = suppliersLoading || productsServicesLoading;

  return (
    <>
      <PermissionBasedSidebarHider />
      <Container className="divide-y p-0">
        {/* Header */}
        <div className="flex items-center justify-between px-6 py-4">
          <div className="flex items-center gap-x-4">
            <Button
              variant="transparent"
              onClick={() =>
                navigate(`/supplier-management/supplier-offerings/`)
              }
              className="p-1"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div>
              <Heading level="h2">Edit Supplier Offering</Heading>
              <Text className="text-ui-fg-subtle">
                {offering.product_service?.name} by {offering.supplier?.name}
              </Text>
            </div>
          </div>
        </div>

        {/* Form */}
        <div className="px-6 py-6">
          <form onSubmit={handleSubmit} className="space-y-8">
            {/* Basic Information */}
            <div className="space-y-6">
              <div>
                <Heading level="h3">Basic Information</Heading>
                <Text className="text-ui-fg-subtle">
                  Core details about this supplier offering
                </Text>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="supplier_id">
                    Supplier <span className="text-ui-fg-error">*</span>
                  </Label>
                  <Select
                    value={formData.supplier_id || ""}
                    onValueChange={(value) =>
                      handleInputChange("supplier_id", value)
                    }
                    disabled={isLoading}
                  >
                    <Select.Trigger>
                      <Select.Value placeholder="Select a supplier" />
                    </Select.Trigger>
                    <Select.Content>
                      {suppliers.map((supplier: any) => (
                        <Select.Item key={supplier.id} value={supplier.id}>
                          <div className="flex flex-col">
                            <span className="font-medium">{supplier.name}</span>
                            <span className="text-sm text-ui-fg-subtle">
                              {supplier.type}
                            </span>
                          </div>
                        </Select.Item>
                      ))}
                    </Select.Content>
                  </Select>
                  {errors.supplier_id && (
                    <Text size="small" className="text-ui-fg-error">
                      {errors.supplier_id}
                    </Text>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="product_service_id">
                    Product/Service <span className="text-ui-fg-error">*</span>
                  </Label>
                  <Select
                    value={formData.product_service_id || ""}
                    onValueChange={(value) =>
                      handleInputChange("product_service_id", value)
                    }
                    disabled={isLoading}
                  >
                    <Select.Trigger>
                      <Select.Value placeholder="Select a product/service" />
                    </Select.Trigger>
                    <Select.Content>
                      {productsServices.map((ps: any) => (
                        <Select.Item key={ps.id} value={ps.id}>
                          <div className="flex flex-col">
                            <span className="font-medium">{ps.name}</span>
                            <span className="text-sm text-ui-fg-subtle">
                              {ps.category?.name} • {ps.type}
                            </span>
                          </div>
                        </Select.Item>
                      ))}
                    </Select.Content>
                  </Select>
                  {errors.product_service_id && (
                    <Text size="small" className="text-ui-fg-error">
                      {errors.product_service_id}
                    </Text>
                  )}
                </div>
              </div>
            </div>

            {/* Enhanced Pricing */}
            <div className="space-y-6">
              <PricingCalculator
                initialData={{
                  commission: formData.commission
                    ? parseFloat(formData.commission) / 100
                    : undefined,
                  publicPrice: formData.public_price
                    ? parseFloat(formData.public_price)
                    : undefined,
                  supplierPrice: formData.supplier_price
                    ? parseFloat(formData.supplier_price)
                    : undefined,
                  netPrice: formData.net_price
                    ? parseFloat(formData.net_price)
                    : undefined,
                  marginRate: formData.margin_rate
                    ? parseFloat(formData.margin_rate) / 100
                    : undefined,
                  sellingPrice: formData.selling_price
                    ? parseFloat(formData.selling_price)
                    : undefined,
                  customPrices: formData.custom_prices,

                  // Currency fields
                  currency: formData.currency,

                  // Selling currency fields
                  sellingCurrency: formData.selling_currency,
                  sellingPriceSellingCurrency:
                    formData.selling_price_selling_currency
                      ? parseFloat(formData.selling_price_selling_currency)
                      : undefined,
                  exchangeRate: formData.exchange_rate
                    ? parseFloat(formData.exchange_rate)
                    : undefined,
                  exchangeRateDate: formData.exchange_rate_date
                    ? new Date(formData.exchange_rate_date)
                    : undefined,
                }}
                onChange={handlePricingChange}
                showCalculations={true}
                costCurrency={formData.currency}
              />

              {/* Notes for Cost Changes - Show when cost is modified */}
              {showChangeReason && (
                <div className="space-y-2 bg-ui-bg-subtle border border-ui-border-base rounded-lg p-4">
                  <Label
                    htmlFor="change_reason"
                    className="text-ui-fg-base font-medium"
                  >
                    📝 Notes for Cost Changes
                  </Label>
                  <Textarea
                    id="change_reason"
                    placeholder="Add notes about why the cost is being changed..."
                    value={formData.change_reason}
                    onChange={(e) =>
                      handleInputChange("change_reason", e.target.value)
                    }
                    className="bg-ui-bg-base"
                    rows={3}
                  />
                  <Text size="small" className="text-ui-fg-muted">
                    💡 These notes will be stored and displayed in the cost
                    history table for future reference and audit purposes.
                  </Text>
                </div>
              )}

              {/* Status and Currency Override */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="status">Status</Label>
                  <Select
                    value={formData.status}
                    onValueChange={(value) =>
                      handleInputChange(
                        "status",
                        value as "active" | "inactive"
                      )
                    }
                  >
                    <Select.Trigger>
                      <Select.Value />
                    </Select.Trigger>
                    <Select.Content>
                      <Select.Item value="active">Active</Select.Item>
                      <Select.Item value="inactive">Inactive</Select.Item>
                    </Select.Content>
                  </Select>
                </div>

                {selectedSupplier?.default_currency && (
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="currency_override"
                      checked={formData.currency_override}
                      onChange={(e) =>
                        handleInputChange("currency_override", e.target.checked)
                      }
                      className="rounded border-gray-300"
                    />
                    <Label
                      htmlFor="currency_override"
                      className="cursor-pointer"
                    >
                      Override supplier's default currency (
                      {selectedSupplier.default_currency})
                    </Label>
                  </div>
                )}
              </div>
            </div>

            {/* Validity Period */}
            <div className="space-y-6">
              <div>
                <Heading level="h3">Validity Period</Heading>
                <Text className="text-ui-fg-subtle">
                  Set when this offering is valid (optional)
                </Text>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="active_from">Active From</Label>
                  <Input
                    id="active_from"
                    type="date"
                    value={formData.active_from}
                    onChange={(e) =>
                      handleInputChange("active_from", e.target.value)
                    }
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="active_to">
                    Active To
                    <span className="text-xs text-ui-fg-subtle ml-1">
                      (Leave empty for open-ended)
                    </span>
                  </Label>
                  <Input
                    id="active_to"
                    type="date"
                    value={formData.active_to}
                    onChange={(e) =>
                      handleInputChange("active_to", e.target.value)
                    }
                  />
                  <div className="flex items-center gap-2">
                    <button
                      type="button"
                      onClick={() => handleInputChange("active_to", "")}
                      className="text-xs text-ui-fg-subtle hover:text-ui-fg-base underline"
                    >
                      Clear (make open-ended)
                    </button>
                    {!formData.active_to && (
                      <Text size="small" className="text-ui-fg-subtle">
                        This offering will have no end date
                      </Text>
                    )}
                  </div>
                  {errors.active_to && (
                    <Text size="small" className="text-ui-fg-error">
                      {errors.active_to}
                    </Text>
                  )}
                </div>
              </div>
            </div>

            {/* Additional Information */}
            <div className="space-y-6">
              <div>
                <Heading level="h3">Additional Information</Heading>
                <Text className="text-ui-fg-subtle">
                  Optional notes and availability information
                </Text>
              </div>

              <div className="space-y-2">
                <Label htmlFor="availability_notes">Availability Notes</Label>
                <Textarea
                  id="availability_notes"
                  placeholder="Enter any notes about availability, restrictions, or special conditions..."
                  value={formData.availability_notes}
                  onChange={(e) =>
                    handleInputChange("availability_notes", e.target.value)
                  }
                  rows={4}
                />
              </div>
            </div>

            {/* Dynamic Fields */}
            {categorySchema.length > 0 && (
              <div className="space-y-6">
                <div>
                  <Heading level="h3">Category-Specific Fields</Heading>
                  <Text className="text-ui-fg-subtle">
                    Additional fields based on the selected product/service
                    category. Fields marked as locked inherit their values from
                    the product/service and cannot be modified.
                  </Text>
                </div>

                <DynamicFieldRenderer
                  schema={categorySchema}
                  values={formData.custom_fields}
                  onChange={handleCustomFieldChange}
                  errors={Object.fromEntries(
                    Object.entries(errors)
                      .filter(([key]) => key.startsWith("custom_field_"))
                      .map(([key, value]) => [
                        key.replace("custom_field_", ""),
                        value,
                      ])
                  )}
                  inheritedValues={selectedProductService?.custom_fields || {}}
                  showInheritanceIndicators={true}
                  fieldContext="supplier"
                />
              </div>
            )}

            {/* Cost History */}
            <div className="space-y-6">
              <div>
                <Heading level="h3">Cost History</Heading>
                <Text className="text-ui-fg-subtle">
                  Track of all cost and currency changes for this offering
                </Text>
              </div>

              <CostHistoryTable
                costHistory={costHistoryResponse?.cost_history || []}
                stats={costHistoryResponse?.stats}
                isLoading={costHistoryLoading}
                showSupplierOffering={false}
              />
            </div>

            {/* Form Actions */}
            <div className="flex items-center justify-end gap-4 pt-6 border-t">
              <Button
                type="button"
                variant="secondary"
                onClick={() =>
                  navigate(`/supplier-management/supplier-offerings/${id}`)
                }
              >
                <X className="h-4 w-4" />
                Cancel
              </Button>
              <Button
                type="submit"
                isLoading={updateSupplierOffering.isPending}
              >
                <Save className="h-4 w-4" />
                Update Offering
              </Button>
            </div>
          </form>
        </div>
      </Container>

      <Toaster />
    </>
  );
};

export const config = defineRouteConfig({
  label: "Edit Supplier Offering",
});

export default EditSupplierOfferingPage;
