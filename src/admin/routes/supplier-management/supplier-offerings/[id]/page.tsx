import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { ArrowLeft } from "@camped-ai/icons";
import { Save, X, Edit, Copy, Trash, Eye } from "lucide-react";
import {
  Container,
  Heading,
  Text,
  Button,
  Input,
  Textarea,
  Select,
  Label,
  Toaster,
  toast,
  Badge,
  FocusModal,
  Prompt,
} from "@camped-ai/ui";
import { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import PermissionBasedSidebarHider from "../../../../widgets/permission-based-sidebar-hider";
import {
  useSupplierOffering,
  useUpdateSupplierOffering,
  useDeleteSupplierOffering,
  useDuplicateSupplierOffering,
} from "../../../../hooks/supplier-products-services/use-supplier-offerings";
import { useSuppliers } from "../../../../hooks/vendor-management/use-suppliers";
import { useProductsServices } from "../../../../hooks/supplier-products-services/use-products-services";
import Dynamic<PERSON>ieldRenderer, {
  type DynamicFieldSchema,
} from "../../../../components/supplier-management/dynamic-field-renderer";
import { formatCustomFieldValue } from "../../../../utils/format-custom-field-value";
import { getCurrencyDisplayName } from "../../../../constants/supplier-form-options";
import { useHotels } from "../../../../hooks/supplier-products-services/use-hotels";
import { useDestinations } from "../../../../hooks/supplier-products-services/use-destinations";
import CostHistoryTable from "../../../../components/supplier-management/cost-history-table";
import { useSupplierOfferingCostHistory } from "../../../../hooks/supplier-products-services/use-supplier-offering-cost-history";
import PricingCalculator from "../../../../components/supplier-management/pricing-calculator";

interface FormData {
  supplier_id: string;
  product_service_id: string;
  active_from: string;
  active_to: string;
  availability_notes: string;

  // Legacy cost field
  cost: string;

  // Enhanced pricing fields
  commission: string;
  public_price: string;
  supplier_price: string;
  net_price: string;
  margin_rate: string;
  selling_price: string;
  custom_prices: Array<{ name: string; price: number }>;

  // Currency fields
  currency: string;
  currency_override: boolean;

  // Selling currency fields
  selling_currency: string;
  selling_price_selling_currency: string;
  exchange_rate: string;
  exchange_rate_date: string;

  status: "Active" | "Inactive";
  custom_fields: Record<string, any>;
}

const SupplierOfferingDetailPage = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();

  // API calls
  const {
    data: offeringResponse,
    isLoading: offeringLoading,
    error,
  } = useSupplierOffering(id!);
  const { data: suppliersResponse } = useSuppliers();
  const { data: productsServicesResponse } = useProductsServices();
  const { data: hotelsResponse } = useHotels({ is_active: true });
  const { data: destinationsResponse } = useDestinations({ is_active: true });

  const updateSupplierOffering = useUpdateSupplierOffering();
  const deleteSupplierOffering = useDeleteSupplierOffering();
  const duplicateSupplierOffering = useDuplicateSupplierOffering();

  // Cost history data
  const { data: costHistoryResponse, isLoading: costHistoryLoading } =
    useSupplierOfferingCostHistory(id!, { limit: 10 });

  // Extract data
  const offering = offeringResponse?.supplier_offering;
  const suppliers = suppliersResponse?.suppliers || [];
  const productsServices = productsServicesResponse?.product_services || [];
  const hotels = hotelsResponse?.hotels || [];
  const destinations = destinationsResponse?.destinations || [];

  const [isEditing, setIsEditing] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [formData, setFormData] = useState<FormData>({
    supplier_id: "",
    product_service_id: "",
    active_from: "",
    active_to: "",
    availability_notes: "",

    // Legacy cost field
    cost: "",

    // Enhanced pricing fields
    commission: "",
    public_price: "",
    supplier_price: "",
    net_price: "",
    margin_rate: "",
    selling_price: "",
    custom_prices: [],

    // Currency fields
    currency: "CHF",
    currency_override: false,

    // Selling currency fields
    selling_currency: "CHF",
    selling_price_selling_currency: "",
    exchange_rate: "1.0",
    exchange_rate_date: "",

    status: "Active",
    custom_fields: {},
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [categorySchema, setCategorySchema] = useState<DynamicFieldSchema[]>(
    []
  );

  // Initialize form data when offering loads
  useEffect(() => {
    if (offering) {
      // Determine the currency to use based on override flag
      let currencyToUse = offering.currency || "CHF";
      const isOverride = offering.currency_override || false;

      // If not overridden and supplier has default currency, use supplier's default
      if (!isOverride && offering.supplier?.default_currency) {
        currencyToUse = offering.supplier.default_currency;
      }

      setFormData({
        supplier_id: offering.supplier_id,
        product_service_id: offering.product_service_id,
        active_from: offering.active_from
          ? offering.active_from.split("T")[0]
          : "",
        active_to: offering.active_to ? offering.active_to.split("T")[0] : "",
        availability_notes: offering.availability_notes || "",

        // Legacy cost field
        cost: offering.cost ? offering.cost.toString() : "",

        // Enhanced pricing fields
        commission: offering.commission
          ? (offering.commission * 100).toString()
          : "",
        public_price: offering.public_price
          ? offering.public_price.toString()
          : "",
        supplier_price: offering.supplier_price
          ? offering.supplier_price.toString()
          : "",
        net_price: offering.net_price ? offering.net_price.toString() : "",
        margin_rate: offering.margin_rate
          ? (offering.margin_rate * 100).toString()
          : "",
        selling_price: offering.selling_price
          ? offering.selling_price.toString()
          : "",
        custom_prices: offering.custom_prices || [],

        // Currency fields
        currency: currencyToUse,
        currency_override: isOverride,

        // Selling currency fields
        selling_currency: offering.selling_currency || currencyToUse,
        selling_price_selling_currency: offering.selling_price_selling_currency
          ? offering.selling_price_selling_currency.toString()
          : "",
        exchange_rate: offering.exchange_rate
          ? offering.exchange_rate.toString()
          : "1.0",
        exchange_rate_date: offering.exchange_rate_date
          ? new Date(offering.exchange_rate_date).toISOString().split("T")[0]
          : "",

        status: offering.status === "active" ? "Active" : "Inactive",
        custom_fields: offering.custom_fields || {},
      });

      // Set category schema if available
      if (offering.product_service?.category?.dynamic_field_schema) {
        const offeringFields =
          offering.product_service.category.dynamic_field_schema.filter(
            (field: any) => field.used_in_supplier_offering
          );
        setCategorySchema(offeringFields);

        // Ensure locked fields have inherited values (fallback for any edge cases)
        const updatedCustomFields = { ...offering.custom_fields };
        offeringFields.forEach((field: any) => {
          if (
            field.locked_in_offerings &&
            offering.product_service?.custom_fields?.[field.key] !== undefined
          ) {
            updatedCustomFields[field.key] =
              offering.product_service.custom_fields[field.key];
          }
        });

        // Update form data if there are any locked field updates
        if (
          JSON.stringify(updatedCustomFields) !==
          JSON.stringify(offering.custom_fields)
        ) {
          setFormData((prev) => ({
            ...prev,
            custom_fields: updatedCustomFields,
          }));
        }
      }
    }
  }, [offering]);

  const handleInputChange = (field: keyof FormData, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  // Handle pricing calculator changes
  const handlePricingChange = (pricingData: any) => {
    setFormData((prev) => ({
      ...prev,
      commission: pricingData.commission
        ? (pricingData.commission * 100).toString()
        : "",
      public_price: pricingData.publicPrice?.toString() || "",
      supplier_price: pricingData.calculatedSupplierPrice?.toString() || "",
      net_price: pricingData.calculatedNetPrice?.toString() || "",
      margin_rate: pricingData.marginRate
        ? (pricingData.marginRate * 100).toString()
        : "",
      selling_price: pricingData.calculatedSellingPrice?.toString() || "",
      custom_prices: pricingData.customPrices || [],

      // Selling currency fields
      selling_currency: pricingData.sellingCurrency || prev.selling_currency,
      selling_price_selling_currency:
        pricingData.calculatedSellingPriceSellingCurrency?.toString() || "",
      exchange_rate: pricingData.exchangeRate?.toString() || prev.exchange_rate,
      exchange_rate_date: pricingData.exchangeRateDate
        ? new Date(pricingData.exchangeRateDate).toISOString().split("T")[0]
        : prev.exchange_rate_date,
    }));
  };

  const handleCustomFieldChange = (fieldKey: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      custom_fields: {
        ...prev.custom_fields,
        [fieldKey]: value,
      },
    }));
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.supplier_id) {
      newErrors.supplier_id = "Supplier is required";
    }

    if (!formData.product_service_id) {
      newErrors.product_service_id = "Product/Service is required";
    }

    // if (!formData.cost || formData.cost.trim() === "") {
    //   newErrors.cost = "Cost is required";
    // } else if (
    //   isNaN(parseFloat(formData.cost)) ||
    //   parseFloat(formData.cost) < 0
    // ) {
    //   newErrors.cost = "Cost must be a valid positive number";
    // }

    if (!formData.currency) {
      newErrors.currency = "Currency is required";
    }

    // Validate date range
    if (formData.active_from && formData.active_to) {
      const fromDate = new Date(formData.active_from);
      const toDate = new Date(formData.active_to);

      if (fromDate >= toDate) {
        newErrors.active_to = "Active To date must be after Active From date";
      }
    }

    // Validate required custom fields (skip locked fields as they're inherited)
    categorySchema.forEach((field) => {
      if (
        field.required &&
        !field.locked_in_offerings &&
        !formData.custom_fields[field.key]
      ) {
        newErrors[`custom_field_${field.key}`] = `${field.label} is required`;
      }
    });
    console.log("newErrors", newErrors);

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      toast.error("Please fix the errors before saving");
      return;
    }

    try {
      const updateData = {
        ...formData,
        active_from: formData.active_from || undefined,
        active_to: formData.active_to || undefined,
        availability_notes: formData.availability_notes || undefined,

        // Legacy cost field
        cost: formData.cost ? parseFloat(formData.cost) : undefined,

        // Enhanced pricing fields
        commission: formData.commission
          ? parseFloat(formData.commission) / 100
          : undefined,
        public_price: formData.public_price
          ? parseFloat(formData.public_price)
          : undefined,
        supplier_price: formData.supplier_price
          ? parseFloat(formData.supplier_price)
          : undefined,
        net_price: formData.net_price
          ? parseFloat(formData.net_price)
          : undefined,
        margin_rate: formData.margin_rate
          ? parseFloat(formData.margin_rate) / 100
          : undefined,
        selling_price: formData.selling_price
          ? parseFloat(formData.selling_price)
          : undefined,
        custom_prices: formData.custom_prices,

        currency: formData.currency,
        currency_override: formData.currency_override,
        status: formData.status.toLowerCase() as "active" | "inactive",
      };

      await updateSupplierOffering.mutateAsync({ id: id!, data: updateData });
      setIsEditing(false);
    } catch (error: any) {
      console.error("Error updating supplier offering:", error);

      // Handle specific error types
      if (
        error?.response?.status === 409 ||
        error?.message?.includes("already exists") ||
        error?.message?.includes("same configuration")
      ) {
        // Extract the detailed error message if available
        let errorMessage =
          "This supplier offering configuration already exists.";

        if (error?.response?.data?.message) {
          errorMessage = error.response.data.message;
        } else if (error?.message) {
          errorMessage = error.message;
        }

        toast.error(errorMessage);
      } else if (error?.response?.status === 400) {
        // Handle validation errors
        let errorMessage = "Invalid data provided.";

        if (error?.response?.data?.message) {
          errorMessage = error.response.data.message;
        } else if (error?.message) {
          errorMessage = error.message;
        }

        toast.error(errorMessage);
      } else {
        toast.error(error?.message || "Failed to update supplier offering");
      }
    }
  };

  const handleCancel = () => {
    if (offering) {
      // Determine the currency to use based on override flag
      let currencyToUse = offering.currency || "CHF";
      const isOverride = offering.currency_override || false;

      // If not overridden and supplier has default currency, use supplier's default
      if (!isOverride && offering.supplier?.default_currency) {
        currencyToUse = offering.supplier.default_currency;
      }

      setFormData({
        supplier_id: offering.supplier_id,
        product_service_id: offering.product_service_id,
        active_from: offering.active_from
          ? offering.active_from.split("T")[0]
          : "",
        active_to: offering.active_to ? offering.active_to.split("T")[0] : "",
        availability_notes: offering.availability_notes || "",

        // Legacy cost field
        cost: offering.cost ? offering.cost.toString() : "",

        // Enhanced pricing fields
        commission: offering.commission
          ? (offering.commission * 100).toString()
          : "",
        public_price: offering.public_price
          ? offering.public_price.toString()
          : "",
        supplier_price: offering.supplier_price
          ? offering.supplier_price.toString()
          : "",
        net_price: offering.net_price ? offering.net_price.toString() : "",
        margin_rate: offering.margin_rate
          ? (offering.margin_rate * 100).toString()
          : "",
        selling_price: offering.selling_price
          ? offering.selling_price.toString()
          : "",
        custom_prices: offering.custom_prices || [],

        // Currency fields
        currency: currencyToUse,
        currency_override: isOverride,

        // Selling currency fields
        selling_currency: offering.selling_currency || currencyToUse,
        selling_price_selling_currency: offering.selling_price_selling_currency
          ? offering.selling_price_selling_currency.toString()
          : "",
        exchange_rate: offering.exchange_rate
          ? offering.exchange_rate.toString()
          : "1.0",
        exchange_rate_date: offering.exchange_rate_date
          ? new Date(offering.exchange_rate_date).toISOString().split("T")[0]
          : "",

        status: offering.status === "active" ? "Active" : "Inactive",
        custom_fields: offering.custom_fields || {},
      });
    }
    setIsEditing(false);
    setErrors({});
  };

  const handleDelete = async () => {
    try {
      await deleteSupplierOffering.mutateAsync(id!);
      navigate("/supplier-management/supplier-offerings");
    } catch (error) {
      console.error("Error deleting supplier offering:", error);
    }
  };

  const handleDuplicate = async () => {
    try {
      await duplicateSupplierOffering.mutateAsync(id!);
      navigate("/supplier-management/supplier-offerings");
    } catch (error) {
      console.error("Error duplicating supplier offering:", error);
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status.toLowerCase()) {
      case "active":
        return "green";
      case "inactive":
        return "red";
      default:
        return "grey";
    }
  };

  const formatDate = (dateStr?: string) => {
    if (!dateStr) return "Not set";
    return new Date(dateStr).toLocaleDateString();
  };

  if (offeringLoading) {
    return (
      <Container className="p-6">
        <div className="text-center">
          <Text>Loading supplier offering...</Text>
        </div>
      </Container>
    );
  }

  if (error || !offering) {
    return (
      <Container className="p-6">
        <div className="text-center">
          <Text className="text-ui-fg-error">
            {error?.message || "Supplier offering not found"}
          </Text>
          <Button
            variant="secondary"
            onClick={() => navigate("/supplier-management/supplier-offerings")}
            className="mt-4"
          >
            Back to Supplier Offerings
          </Button>
        </div>
      </Container>
    );
  }

  return (
    <>
      <PermissionBasedSidebarHider />
      <Container className="divide-y p-0">
        {/* Header */}
        <div className="flex items-center justify-between px-6 py-4">
          <div className="flex items-center gap-x-4">
            <Button
              variant="transparent"
              onClick={() =>
                navigate("/supplier-management/supplier-offerings")
              }
              className="p-1"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div>
              <Heading level="h2">
                {isEditing
                  ? "Edit Supplier Offering"
                  : "Supplier Offering Details"}
              </Heading>
              <Text className="text-ui-fg-subtle">
                {offering.product_service?.name} by {offering.supplier?.name}
              </Text>
            </div>
          </div>
          <div className="flex items-center gap-x-2">
            {isEditing ? (
              <>
                <Button variant="secondary" onClick={handleCancel}>
                  <X className="h-4 w-4" />
                  Cancel
                </Button>
                <Button
                  onClick={handleSave}
                  isLoading={updateSupplierOffering.isPending}
                >
                  <Save className="h-4 w-4" />
                  Save Changes
                </Button>
              </>
            ) : (
              <>
                {/* <Button
                  variant="secondary"
                  size="small"
                  onClick={handleDuplicate}
                  isLoading={duplicateSupplierOffering.isPending}
                >
                  <Copy className="h-4 w-4" />
                  Duplicate
                </Button> */}
                <Button
                  variant="secondary"
                  size="small"
                  onClick={() => setIsEditing(true)}
                >
                  <Edit className="h-4 w-4" />
                  Edit
                </Button>
                <Button
                  variant="danger"
                  size="small"
                  onClick={() => setShowDeleteModal(true)}
                >
                  <Trash className="h-4 w-4" />
                  Delete
                </Button>
              </>
            )}
          </div>
        </div>

        {/* Content */}
        <div className="px-6 py-6">
          <div className="space-y-8">
            {/* Basic Information */}
            <div className="space-y-6">
              <div>
                <Heading level="h3">Basic Information</Heading>
                <Text className="text-ui-fg-subtle">
                  Core details about this supplier offering
                </Text>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label>Supplier</Label>
                  {isEditing ? (
                    <Select
                      value={formData.supplier_id || ""}
                      onValueChange={(value) =>
                        handleInputChange("supplier_id", value)
                      }
                    >
                      <Select.Trigger>
                        <Select.Value placeholder="Select a supplier" />
                      </Select.Trigger>
                      <Select.Content>
                        {suppliers.map((supplier: any) => (
                          <Select.Item key={supplier.id} value={supplier.id}>
                            {supplier.name} ({supplier.type})
                          </Select.Item>
                        ))}
                      </Select.Content>
                    </Select>
                  ) : (
                    <div className="p-3 bg-ui-bg-subtle rounded-lg">
                      <Text weight="plus">{offering.supplier?.name}</Text>
                      <Text size="small" className="text-ui-fg-subtle">
                        {offering.supplier?.type} •{" "}
                        {offering.supplier?.primary_contact_email}
                      </Text>
                    </div>
                  )}
                  {errors.supplier_id && (
                    <Text size="small" className="text-ui-fg-error">
                      {errors.supplier_id}
                    </Text>
                  )}
                </div>

                <div className="space-y-2">
                  <Label>Product/Service</Label>
                  {isEditing ? (
                    <Select
                      value={formData.product_service_id || ""}
                      onValueChange={(value) =>
                        handleInputChange("product_service_id", value)
                      }
                    >
                      <Select.Trigger>
                        <Select.Value placeholder="Select a product/service" />
                      </Select.Trigger>
                      <Select.Content>
                        {productsServices.map((ps: any) => (
                          <Select.Item key={ps.id} value={ps.id}>
                            {ps.name} ({ps.type})
                          </Select.Item>
                        ))}
                      </Select.Content>
                    </Select>
                  ) : (
                    <div className="p-3 bg-ui-bg-subtle rounded-lg">
                      <Text weight="plus">
                        {offering.product_service?.name}
                      </Text>
                      <div className="flex items-center gap-2 mt-1">
                        <Badge variant="grey" size="small">
                          {offering.product_service?.type}
                        </Badge>
                        <Badge variant="blue" size="small">
                          {offering.product_service?.category?.name}
                        </Badge>
                      </div>
                    </div>
                  )}
                  {errors.product_service_id && (
                    <Text size="small" className="text-ui-fg-error">
                      {errors.product_service_id}
                    </Text>
                  )}
                </div>
              </div>
            </div>

            {/* Enhanced Pricing */}
            <div className="space-y-6">
              {isEditing ? (
                <PricingCalculator
                  initialData={{
                    commission: formData.commission
                      ? parseFloat(formData.commission) / 100
                      : undefined,
                    publicPrice: formData.public_price
                      ? parseFloat(formData.public_price)
                      : undefined,
                    supplierPrice: formData.supplier_price
                      ? parseFloat(formData.supplier_price)
                      : undefined,
                    netPrice: formData.net_price
                      ? parseFloat(formData.net_price)
                      : undefined,
                    marginRate: formData.margin_rate
                      ? parseFloat(formData.margin_rate) / 100
                      : undefined,
                    sellingPrice: formData.selling_price
                      ? parseFloat(formData.selling_price)
                      : undefined,
                    customPrices: formData.custom_prices,

                    // Currency fields
                    currency: formData.currency,

                    // Selling currency fields
                    sellingCurrency: formData.selling_currency,
                    sellingPriceSellingCurrency:
                      formData.selling_price_selling_currency
                        ? parseFloat(formData.selling_price_selling_currency)
                        : undefined,
                    exchangeRate: formData.exchange_rate
                      ? parseFloat(formData.exchange_rate)
                      : undefined,
                    exchangeRateDate: formData.exchange_rate_date
                      ? new Date(formData.exchange_rate_date)
                      : undefined,
                  }}
                  onChange={handlePricingChange}
                  showCalculations={true}
                  costCurrency={formData.currency}
                />
              ) : (
                <div className="space-y-6">
                  <div>
                    <Heading level="h3">Pricing Information</Heading>
                    <Text className="text-ui-fg-subtle">
                      Current pricing details for this offering
                    </Text>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {/* Legacy Cost */}
                    {offering.cost && (
                      <div className="p-4 bg-ui-bg-subtle rounded-lg">
                        <Text size="small" className="text-ui-fg-subtle">
                          Legacy Cost
                        </Text>
                        <Text weight="plus" size="large">
                          {offering.cost}{" "}
                          {getCurrencyDisplayName(offering.currency)}
                        </Text>
                      </div>
                    )}

                    {/* Commission & Public Price */}
                    {offering.commission && offering.public_price && (
                      <>
                        <div className="p-4 bg-ui-bg-subtle rounded-lg">
                          <Text size="small" className="text-ui-fg-subtle">
                            Commission
                          </Text>
                          <Text weight="plus" size="large">
                            {(offering.commission * 100).toFixed(2)}%
                          </Text>
                        </div>
                        <div className="p-4 bg-ui-bg-subtle rounded-lg">
                          <Text size="small" className="text-ui-fg-subtle">
                            Public Price
                          </Text>
                          <Text weight="plus" size="large">
                            {offering.public_price}{" "}
                            {getCurrencyDisplayName(offering.currency)}
                          </Text>
                        </div>
                      </>
                    )}

                    {/* Calculated Prices */}
                    {offering.supplier_price && (
                      <div className="p-4 bg-blue-50 rounded-lg">
                        <Text size="small" className="text-ui-fg-subtle">
                          Supplier Price
                        </Text>
                        <Text weight="plus" size="large">
                          {offering.supplier_price}{" "}
                          {getCurrencyDisplayName(offering.currency)}
                        </Text>
                      </div>
                    )}

                    {offering.net_price && (
                      <div className="p-4 bg-green-50 rounded-lg">
                        <Text size="small" className="text-ui-fg-subtle">
                          Net Price
                        </Text>
                        <Text weight="plus" size="large">
                          {offering.net_price}{" "}
                          {getCurrencyDisplayName(offering.currency)}
                        </Text>
                      </div>
                    )}

                    {offering.selling_price && (
                      <div className="p-4 bg-purple-50 rounded-lg">
                        <Text size="small" className="text-ui-fg-subtle">
                          Selling Price (Cost Currency -{" "}
                          {getCurrencyDisplayName(offering.currency)})
                        </Text>
                        <Text weight="plus" size="large">
                          {offering.selling_price}{" "}
                          {getCurrencyDisplayName(offering.currency)}
                        </Text>
                        {offering.margin_rate && (
                          <Text size="small" className="text-ui-fg-subtle">
                            Margin: {(offering.margin_rate * 100).toFixed(2)}%
                          </Text>
                        )}
                      </div>
                    )}

                    {/* Selling Currency Price */}
                    {offering.selling_currency &&
                      offering.selling_currency !== offering.currency &&
                      offering.selling_price_selling_currency && (
                        <div className="p-4 bg-orange-50 rounded-lg border-2 border-orange-200">
                          <Text size="small" className="text-ui-fg-subtle">
                            Selling Price (Selling Currency -{" "}
                            {getCurrencyDisplayName(offering.selling_currency)})
                          </Text>
                          <Text weight="plus" size="large">
                            {offering.selling_price_selling_currency}{" "}
                            {getCurrencyDisplayName(offering.selling_currency)}
                          </Text>
                          {offering.exchange_rate && (
                            <Text size="small" className="text-ui-fg-subtle">
                              Exchange Rate:{" "}
                              {parseFloat(
                                offering.exchange_rate.toString()
                              ).toFixed(4)}
                            </Text>
                          )}
                          {offering.exchange_rate_date && (
                            <Text size="small" className="text-ui-fg-subtle">
                              Updated:{" "}
                              {new Date(
                                offering.exchange_rate_date
                              ).toLocaleDateString()}
                            </Text>
                          )}
                        </div>
                      )}
                  </div>

                  {/* Custom Prices */}
                  {offering.custom_prices &&
                    offering.custom_prices.length > 0 && (
                      <div className="space-y-3">
                        <Text weight="plus">Custom Additional Prices</Text>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                          {offering.custom_prices.map(
                            (customPrice: any, index: number) => (
                              <div
                                key={index}
                                className="flex justify-between items-center p-3 bg-ui-bg-base border rounded-lg"
                              >
                                <Text>{customPrice.name}</Text>
                                <Text weight="plus">
                                  {customPrice.price}{" "}
                                  {getCurrencyDisplayName(offering.currency)}
                                </Text>
                              </div>
                            )
                          )}
                        </div>
                      </div>
                    )}

                  {/* Currency Info */}
                  <div className="space-y-4">
                    <div className="p-4 bg-ui-bg-subtle rounded-lg">
                      <Text size="small" className="text-ui-fg-subtle">
                        Cost Currency
                      </Text>
                      <Text weight="plus">
                        {offering.currency
                          ? getCurrencyDisplayName(offering.currency)
                          : "Not set"}
                      </Text>
                      {offering.currency_override && (
                        <Text size="small" className="text-ui-fg-subtle">
                          Override from supplier default
                        </Text>
                      )}
                    </div>

                    {/* Selling Currency Info */}
                    {offering.selling_currency && (
                      <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                        <Text size="small" className="text-ui-fg-subtle">
                          Selling Currency
                        </Text>
                        <Text weight="plus">
                          {getCurrencyDisplayName(offering.selling_currency)}
                        </Text>
                        {offering.selling_currency !== offering.currency && (
                          <>
                            {offering.exchange_rate && (
                              <Text size="small" className="text-ui-fg-subtle">
                                Exchange Rate: 1 {offering.currency} ={" "}
                                {parseFloat(
                                  offering.exchange_rate.toString()
                                ).toFixed(4)}{" "}
                                {offering.selling_currency}
                              </Text>
                            )}
                            {offering.exchange_rate_date && (
                              <Text size="small" className="text-ui-fg-subtle">
                                Rate updated:{" "}
                                {new Date(
                                  offering.exchange_rate_date
                                ).toLocaleDateString()}
                              </Text>
                            )}
                          </>
                        )}
                        {offering.selling_currency === offering.currency && (
                          <Text size="small" className="text-ui-fg-subtle">
                            Same as cost currency
                          </Text>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>

            {/* Validity Period */}
            <div className="space-y-6">
              <div>
                <Heading level="h3">Validity Period</Heading>
                <Text className="text-ui-fg-subtle">
                  When this offering is active
                </Text>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label>Active From</Label>
                  {isEditing ? (
                    <Input
                      type="date"
                      value={formData.active_from}
                      onChange={(e) =>
                        handleInputChange("active_from", e.target.value)
                      }
                    />
                  ) : (
                    <div className="p-3 bg-ui-bg-subtle rounded-lg">
                      <Text>{formatDate(offering.active_from)}</Text>
                    </div>
                  )}
                  {errors.active_from && (
                    <Text size="small" className="text-ui-fg-error">
                      {errors.active_from}
                    </Text>
                  )}
                </div>

                <div className="space-y-2">
                  <Label>Active To</Label>
                  {isEditing ? (
                    <Input
                      type="date"
                      value={formData.active_to}
                      onChange={(e) =>
                        handleInputChange("active_to", e.target.value)
                      }
                    />
                  ) : (
                    <div className="p-3 bg-ui-bg-subtle rounded-lg">
                      <Text>{formatDate(offering.active_to)}</Text>
                    </div>
                  )}
                  {errors.active_to && (
                    <Text size="small" className="text-ui-fg-error">
                      {errors.active_to}
                    </Text>
                  )}
                </div>
              </div>
            </div>

            {/* Availability & Status */}
            <div className="space-y-6">
              <div>
                <Heading level="h3">Availability & Status</Heading>
                <Text className="text-ui-fg-subtle">
                  Additional information and current status
                </Text>
              </div>

              <div className="space-y-6">
                <div className="space-y-2">
                  <Label>Availability Notes</Label>
                  {isEditing ? (
                    <Textarea
                      placeholder="Optional notes about availability, restrictions, or special conditions..."
                      value={formData.availability_notes}
                      onChange={(e) =>
                        handleInputChange("availability_notes", e.target.value)
                      }
                      rows={3}
                    />
                  ) : (
                    <div className="p-3 bg-ui-bg-subtle rounded-lg min-h-[80px]">
                      <Text>
                        {offering.availability_notes ||
                          "No availability notes provided"}
                      </Text>
                    </div>
                  )}
                </div>

                <div className="space-y-2">
                  <Label>Status</Label>
                  {isEditing ? (
                    <div className="flex gap-6">
                      <label className="flex items-center gap-2 cursor-pointer">
                        <input
                          type="radio"
                          name="status"
                          value="active"
                          checked={formData.status === "active"}
                          onChange={(e) =>
                            handleInputChange("status", e.target.value)
                          }
                          className="w-4 h-4 text-ui-fg-interactive border-ui-border-base focus:ring-ui-border-interactive"
                        />
                        <Text size="small">Active</Text>
                      </label>
                      <label className="flex items-center gap-2 cursor-pointer">
                        <input
                          type="radio"
                          name="status"
                          value="inactive"
                          checked={formData.status === "inactive"}
                          onChange={(e) =>
                            handleInputChange("status", e.target.value)
                          }
                          className="w-4 h-4 text-ui-fg-interactive border-ui-border-base focus:ring-ui-border-interactive"
                        />
                        <Text size="small">Inactive</Text>
                      </label>
                    </div>
                  ) : (
                    <div>
                      <Badge variant={getStatusBadgeVariant(offering.status)}>
                        {offering.status}
                      </Badge>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Dynamic Custom Fields */}
            {categorySchema.length > 0 && (
              <div className="space-y-6">
                <div>
                  <Heading level="h3">Category-Specific Fields</Heading>
                  <Text className="text-ui-fg-subtle">
                    Additional fields based on the category
                  </Text>
                </div>

                {isEditing ? (
                  <DynamicFieldRenderer
                    schema={categorySchema}
                    values={formData.custom_fields}
                    onChange={handleCustomFieldChange}
                    errors={errors}
                    inheritedValues={
                      offering?.product_service?.custom_fields || {}
                    }
                    showInheritanceIndicators={true}
                  />
                ) : (
                  <div className="space-y-4">
                    {categorySchema.map((field) => (
                      <div key={field.key} className="space-y-2">
                        <Label>{field.label}</Label>
                        <div className="p-3 bg-ui-bg-subtle rounded-lg">
                          <Text>
                            {formatCustomFieldValue(
                              offering.custom_fields?.[field.key],
                              field,
                              { hotels, destinations }
                            ) || "Not set"}
                          </Text>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {/* Cost History */}
            {!isEditing && (
              <div id="cost-history" className="space-y-6">
                <div>
                  <Heading level="h3">Cost History</Heading>
                  <Text className="text-ui-fg-subtle">
                    Track of all cost and currency changes for this offering
                  </Text>
                </div>

                <CostHistoryTable
                  costHistory={costHistoryResponse?.cost_history || []}
                  stats={costHistoryResponse?.stats}
                  isLoading={costHistoryLoading}
                  showSupplierOffering={false}
                />
              </div>
            )}

            {/* Audit Information */}
            {!isEditing && (
              <div className="space-y-6">
                <div>
                  <Heading level="h3">Audit Information</Heading>
                  <Text className="text-ui-fg-subtle">
                    Creation and modification history
                  </Text>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label>Created</Label>
                    <div className="p-3 bg-ui-bg-subtle rounded-lg">
                      <Text>{formatDate(offering.created_at)}</Text>
                      {offering.created_by && (
                        <Text size="small" className="text-ui-fg-subtle">
                          by {offering.created_by}
                        </Text>
                      )}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Last Updated</Label>
                    <div className="p-3 bg-ui-bg-subtle rounded-lg">
                      <Text>{formatDate(offering.updated_at)}</Text>
                      {offering.updated_by && (
                        <Text size="small" className="text-ui-fg-subtle">
                          by {offering.updated_by}
                        </Text>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </Container>

      {/* Delete Confirmation Prompt */}
      <Prompt open={showDeleteModal} onOpenChange={setShowDeleteModal}>
        <Prompt.Content>
          <Prompt.Header>
            <Prompt.Title>Delete Supplier Offering</Prompt.Title>
            <Prompt.Description>
              Are you sure you want to delete this supplier offering? This
              action cannot be undone.
              {offering && (
                <div className="mt-2 text-sm font-medium">
                  {offering.product_service?.name} by {offering.supplier?.name}
                </div>
              )}
            </Prompt.Description>
          </Prompt.Header>
          <Prompt.Footer>
            <Prompt.Cancel onClick={() => setShowDeleteModal(false)}>
              Cancel
            </Prompt.Cancel>
            <Prompt.Action
              onClick={handleDelete}
              disabled={deleteSupplierOffering.isPending}
            >
              {deleteSupplierOffering.isPending ? "Deleting..." : "Delete"}
            </Prompt.Action>
          </Prompt.Footer>
        </Prompt.Content>
      </Prompt>

      <Toaster />
    </>
  );
};

export const config = defineRouteConfig({
  label: "Supplier Offering Details",
});

export default SupplierOfferingDetailPage;
